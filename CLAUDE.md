# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

SnapOffer 是一个AI驱动的求职智能体，基于 Next.js 15 和现代技术栈构建。项目采用 App Router 架构，支持多AI模型集成，提供完整的简历构建器、岗位申请管理、智能应聘攻略功能，帮助求职者快速获得Offer。

## 常用开发命令

### 核心命令
- `npm run dev` - 启动开发服务器（禁用TLS验证，使用Turbopack）
- `npm run build` - 构建生产版本
- `npm run start` - 启动生产服务器
- `npm run lint` - 运行ESLint检查
- `npm run test:proxy` - 测试代理功能

### 环境要求
- Node.js >= 20.0.0
- PostgreSQL 数据库（通过 Supabase）
- Stripe 账户（支付功能）

## 项目架构

### 技术栈
- **前端**: Next.js 15 (App Router) + React 19 + TypeScript
- **样式**: Tailwind CSS + Shadcn UI (New York 主题)
- **数据库**: PostgreSQL + Supabase (包含认证和行级安全)
- **AI集成**: Vercel AI SDK (支持 OpenAI, Anthropic, Google, DeepSeek, Groq)
- **PDF生成**: @react-pdf/renderer + html2pdf.js
- **富文本编辑**: TipTap
- **支付**: Stripe
- **状态管理**: React Context + Server Actions

### 目录结构
```
src/
├── app/                    # Next.js App Router 页面
│   ├── (dashboard)/       # 仪表板路由组（需要认证）
│   ├── admin/             # 管理后台
│   ├── auth/              # 认证相关页面
│   ├── api/               # API 路由
│   └── blog/              # 博客页面
├── components/            # React 组件
│   ├── auth/              # 认证组件
│   ├── resume/            # 简历编辑器组件
│   ├── dashboard/         # 仪表板组件
│   ├── landing/           # 首页组件
│   ├── ui/                # 基础UI组件 (Shadcn)
│   └── layout/            # 布局组件
├── lib/                   # 工具库和配置
│   ├── pdf/               # PDF 生成相关
│   ├── prompts.ts         # AI 提示词模板
│   ├── tools.ts           # AI 工具函数
│   └── types.ts           # TypeScript 类型定义
├── utils/                 # 实用工具函数
│   ├── actions/           # Server Actions
│   └── supabase/          # Supabase 配置
└── hooks/                 # React Hooks
```

### 数据库架构
主要数据表：
- `users` - 用户认证信息（Supabase Auth）
- `profiles` - 用户详细资料
- `resumes` - 简历数据（包含工作经验、教育、技能等JSON字段）
- `subscriptions` - 订阅信息（Stripe集成）
- `jobs` - 职位信息

### 关键功能模块
1. **用户认证系统** - 基于 Supabase Auth，支持邮箱/密码登录、密码重置
2. **简历构建器** - 实时预览、拖拽编辑、富文本支持、AI生成和优化
3. **岗位申请管理** - 职位信息管理、分类筛选、求职进度跟踪、管理员权限
4. **智能应聘攻略** - AI驱动的面试准备、STAR框架优化、问题预测、答案生成
5. **AI助手** - 多模型支持，简历定制化、岗位匹配、速率限制管理
6. **PDF生成** - 支持多种格式，ATS优化、多种生成方式
7. **求职信功能** - AI生成和编辑求职信
8. **订阅管理** - Stripe支付集成，免费/Pro版本、功能权限控制
9. **管理后台** - 用户管理、订阅管理、职位管理
10. **性能优化** - Redis缓存、速率限制、认证缓存

### AI集成
项目使用 Vercel AI SDK 集成多个AI提供商：
- OpenAI (GPT模型)
- Anthropic (Claude)
- Google (Gemini)
- DeepSeek
- Groq
- OpenRouter

AI功能位于 `src/lib/tools.ts` 和 `src/lib/prompts.ts`，提供岗位分析、简历定制化优化、STAR框架优化、应聘攻略生成、速率限制管理等功能。支持免费用户和Pro用户的不同功能权限。

### 样式系统
- 使用 Tailwind CSS 和自定义INS风格配色
- Shadcn UI 组件库（New York主题）
- 响应式设计，移动优先
- Framer Motion 动画效果
- **设计令牌系统**：统一管理颜色、间距、圆角、阴影等设计变量
- **极简设计理念**：85%黑白+5%灰色+10%品牌蓝的严格比例控制
- **类型安全**：提供完整的设计令牌类型定义和工具函数

### 开发注意事项
1. **代码风格**: 遵循项目现有的TypeScript和React模式
2. **组件开发**: 优先使用Server Components，必要时使用Client Components
3. **状态管理**: 使用React Context和Server Actions，避免过度使用客户端状态
4. **样式**: 使用Tailwind CSS类，遵循已有的设计系统
5. **设计令牌**: 必须使用设计令牌系统管理所有设计变量，严格遵循颜色使用比例
6. **类型安全**: 充分利用TypeScript类型检查
7. **AI功能**: 新增AI功能时，在`src/lib/prompts.ts`中添加提示词模板，重点关注岗位分析和应聘策略相关功能

## 设计规范指南

### 设计理念
SnapOffer 采用极简扁平设计风格，专注于 **85% 黑白 + 5% 灰色 + 10% 品牌蓝** 的色彩比例，结合 Shadcn UI (New York 主题)/Vercel 的设计理念，创建清晰、现代的用户界面。

### 设计令牌系统
项目使用统一的设计令牌系统，位于 `src/lib/design-tokens.ts`，管理所有设计变量：

#### 核心设计令牌
- **颜色令牌**: 品牌蓝（10%）、灰色（5%）、黑白（85%）
- **间距令牌**: 从 1px 到 16rem 的统一间距系统
- **圆角令牌**: 从无圆角到完全圆形的圆角系统
- **阴影令牌**: 从无阴影到极大阴影的阴影系统
- **字体令牌**: 完整的字体、大小、粗细、行高系统
- **动画令牌**: 统一的持续时间和缓动函数

### 组件开发规范

#### 1. 组件结构规范
```tsx
// ✅ 标准组件结构
'use client';

import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export function MyComponent() {
  return (
    <Card className="bg-white border-gray-200 shadow-md rounded-lg">
      <CardHeader>
        <CardTitle className="text-black text-lg">组件标题</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-gray-600">组件内容</p>
          <Button className="bg-blue-500 hover:bg-blue-600 text-white">
            操作按钮
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
```

#### 2. Shadcn UI 组件使用规范
- **Button**: 主要操作使用品牌蓝，次要操作使用灰色
- **Card**: 使用统一的边框、阴影和圆角
- **Input**: 统一的边框、圆角和焦点样式
- **Dialog**: 一致的背景、边框和阴影
- **Select**: 统一的下拉样式和选项样式

#### 3. 颜色使用规范
- **品牌蓝（10%）**: 主要CTA按钮、链接文字、选中状态、焦点环
- **灰色（5%）**: 次要按钮、边框、禁用状态、次要文字
- **黑白（85%）**: 主要背景、主要文字、卡片背景

#### 4. 间距和布局规范
- **使用间距令牌**: 优先使用 `space-y-*`、`p-*`、`m-*` 等Tailwind类
- **响应式设计**: 使用移动端优先的断点系统
- **Flexbox/Grid**: 使用现代布局系统，避免固定宽度

#### 5. 状态管理规范
- **Server Components**: 优先使用服务端组件
- **Server Actions**: 使用服务端操作处理数据
- **React Context**: 仅在必要时使用客户端状态
- **避免过度状态**: 不要在客户端存储不必要的状态

### 样式开发最佳实践

#### 1. 类名组织规范
```tsx
// ✅ 按功能、布局、状态顺序组织类名
<div className="flex justify-between items-center p-4 bg-white border-gray-200 rounded-lg hover:shadow-md transition-shadow">
  <h2 className="text-black text-lg font-semibold">标题</h2>
  <Button className="bg-blue-500 hover:bg-blue-600 text-white">操作</Button>
</div>
```

#### 2. 响应式设计规范
```tsx
// ✅ 移动端优先的响应式设计
<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">卡片 1</Card>
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">卡片 2</Card>
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">卡片 3</Card>
</div>
```

#### 3. 动画和过渡规范
```tsx
// ✅ 统一的动画效果
<div className="transition-all duration-300 ease-in-out hover:shadow-lg hover:-translate-y-1">
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">
    卡片内容
  </Card>
</div>
```

### 性能优化规范

#### 1. 代码分割
```tsx
// ✅ 动态导入大型组件
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  ssr: false,
  loading: () => <div>加载中...</div>,
});
```

#### 2. 图片优化
```tsx
// ✅ 使用 Next.js Image 组件
import Image from 'next/image';

<Image src="/image.jpg" alt="描述" width={500} height={300} loading="lazy" />
```

#### 3. 样式优化
- **优先使用 Tailwind 类**: 避免自定义 CSS
- **合理使用动画**: 只在必要时使用动画效果
- **优化 CSS 体积**: 避免未使用的样式

### 可访问性规范

#### 1. 语义化 HTML
```tsx
// ✅ 使用语义化标签
<nav aria-label="主导航">
  <ul>
    <li><a href="#" className="text-blue-500 hover:underline">首页</a></li>
    <li><a href="#" className="text-blue-500 hover:underline">关于</a></li>
  </ul>
</nav>
```

#### 2. 键盘导航
```tsx
// ✅ 确保键盘可访问
<Button className="focus:outline-none focus:ring-2 focus:ring-blue-500">
  焦点按钮
</Button>
```

#### 3. 对比度要求
- **正文文字**: 对比度至少 21:1
- **次要文字**: 对比度至少 4.5:1
- **链接文字**: 对比度至少 4.5:1
- **按钮文字**: 对比度至少 4.5:1

### 测试规范

#### 1. 单元测试
- **组件测试**: 测试组件的渲染和交互
- **工具函数测试**: 测试设计令牌和工具函数
- **状态管理测试**: 测试状态变更和副作用

#### 2. 集成测试
- **页面测试**: 测试完整页面的功能
- **用户流程测试**: 测试完整的用户操作流程
- **API 测试**: 测试 API 接口和数据处理

#### 3. 可访问性测试
- **对比度测试**: 使用工具验证颜色对比度
- **键盘导航测试**: 验证键盘操作的完整性
- **屏幕阅读器测试**: 验证屏幕阅读器的兼容性

### 文档规范

#### 1. 组件文档
- **用途说明**: 组件的使用场景和目的
- **Props 说明**: 组件的属性和类型定义
- **使用示例**: 完整的使用示例代码
- **注意事项**: 使用时需要注意的问题

#### 2. 设计令牌文档
- **令牌说明**: 每个设计令牌的用途和含义
- **使用示例**: 具体的使用示例
- **最佳实践**: 推荐的使用方式
- **注意事项**: 使用时需要遵循的规则

### 常见问题解决

#### 1. 样式不一致问题
- **检查设计令牌**: 确保使用了正确的设计令牌
- **统一类名**: 使用统一的类名和样式
- **避免硬编码**: 不直接使用颜色值和尺寸值

#### 2. 性能问题
- **代码分割**: 使用动态导入分割大型组件
- **图片优化**: 使用 Next.js Image 组件
- **样式优化**: 避免未使用的样式

#### 3. 可访问性问题
- **对比度检查**: 使用工具验证颜色对比度
- **键盘导航**: 确保所有交互都可以通过键盘操作
- **语义化标签**: 使用正确的 HTML 标签

### 工具和资源

#### 1. 开发工具
- **VS Code**: 代码编辑器
- **TypeScript**: 类型检查
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

#### 2. 设计工具
- **Figma**: 设计原型
- **Tailwind CSS**: 样式框架
- **Shadcn UI**: 组件库

#### 3. 测试工具
- **Jest**: 单元测试
- **Playwright**: E2E 测试
- **Axe Core**: 可访问性测试

#### 4. 文档资源
- **DESIGN_TOKENS_USAGE.md**: 设计令牌使用指南
- **src/lib/design-tokens.ts**: 设计令牌定义
- **src/config/colors.ts**: 颜色配置文件

### 重要配置文件
- `next.config.ts` - Next.js配置（包含MDX支持）
- `tailwind.config.ts` - Tailwind CSS配置（自定义颜色和动画）
- `components.json` - Shadcn UI配置
- `src/lib/design-tokens.ts` - 设计令牌系统（统一管理设计变量）
- `src/config/colors.ts` - 颜色配置（使用设计令牌）
- `DESIGN_TOKENS_USAGE.md` - 设计令牌使用指南
- `src/utils/supabase/` - Supabase客户端配置

## Steering Documents

### 产品概述 (`.claude/steering/product.md`)
SnapOffer的产品定位、核心价值主张、主要功能模块、目标用户和商业模式。包含简历构建器、岗位申请管理、智能应聘攻略、AI助手系统等核心功能的详细说明。已更新最新功能：职位管理分类、STAR框架优化、求职信功能等。

### 技术栈 (`.claude/steering/tech.md`)
项目的技术架构、构建系统、开发环境和工具链。包含Next.js 15、React 19、TypeScript、Tailwind CSS、Supabase、Vercel AI SDK等技术栈的详细配置和使用说明。已更新最新依赖：OpenRouter支持、Redis缓存、PDF处理库、性能优化工具等。

### 项目结构 (`.claude/steering/structure.md`)
详细的目录组织结构、文件命名约定、组件组织原则和数据流架构。包含关键文件位置、模块说明和开发规范。已更新最新结构：职位管理组件、求职信功能、性能优化模块、缓存系统等。