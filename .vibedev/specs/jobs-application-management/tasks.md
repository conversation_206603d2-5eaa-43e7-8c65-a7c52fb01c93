### 岗位申请管理 MVP — 实施任务清单（编码可执行）

说明：将设计转化为可由代码生成/编码代理执行的、增量式与可测试的任务。仅包含编写/修改/测试代码的活动；每步引用具体需求条款（见 requirements.md）。

1. [x] 初始化前端页面骨架与阶段前端过滤（/jobs）
   - 目标：在 `src/app/(dashboard)/jobs/page.tsx` 新建/扩展页面骨架，注入"阶段"前端过滤，不改现有 `JobListingsCard` 的服务端筛选行为（复用工作地点/职位类型）
   - 子项：
     - 1.1 [x] 新建本地 `useState<'全部'|'申请'|'笔试'|'面试'|'谈判'|'录用'|'拒绝'>` 与下拉选择 UI，并对 `getJobListings` 返回集合做前端过滤渲染（Req 3.1-4）
     - 1.2 [x] 添加"添加申请"按钮占位（触发后续集成 AI 的流程），仅渲染 UI（Req 3.1-1, 3.2-1）
     - 1.3 [x] 单元测试：阶段过滤纯函数（输入 jobs 数组，输出过滤结果）（Req 3.1-4）
     - 1.4 [x] 可视化验收：页面顶部出现"阶段"中文下拉；更换选项时，卡片列表即时按阶段过滤；右上出现"添加申请"按钮（Req 3.1-1, 3.1-4）

2. [x] 新增阶段选择器组件（系统阶段 + 自定义阶段）
   - 目标：新增 `src/components/jobs/stage-selector.tsx`，提供展开面板、系统阶段单选、自定义阶段列表与"+ 添加"、✎时间入口（不含实际写入）
   - 子项：
     - 2.1 [x] 组件 UI（依赖 `ui/select`, `ui/radio-group`），可受控 `value`/`onChange`（Req 3.3-1）
     - 2.2 [x] 计算"当前自定义阶段集合"的纯函数（由日志事件 add/remove 得出）（Req 4 数据模型）
     - 2.3 [x] 单元测试：对日志事件序列计算集合与顺序；空与重复操作的边界用例（Req 3.3-3, 4）
     - 2.4 [x] 可视化验收：点击卡片"阶段 ▼"出现面板；录入自定义阶段名称点击"添加"后，面板内标签立即出现/移除（本地态）（Req 3.2-3, 3.3-1, 3.3-3）

3. [x] 新增详情 Modal 及 Tabs 容器
   - 目标：新增 `src/components/jobs/job-details-modal.tsx`（中央单层 Modal），含顶部固定区与 Tabs：概览/简历/笔记/攻略占位
   - 子项：
     - 3.1 [x] Modal 容器与可达性（聚焦捕获、Esc 关闭、阻止未保存退出）（Req 3.4-1, 3.10-1）
     - 3.2 [x] 顶部区：标题@公司 + 收起态阶段控件 + 主按钮/添加笔记按钮占位（Req 3.4-1, 3.4-2）
     - 3.3 [x] Tabs 结构与路由内状态保持（Req 3.4-3）
     - 3.4 [x] 可视化验收：点击任一卡片主体弹出中央 Modal；Esc 可关闭；四个 Tab 可切换且内容容器随之变化（Req 3.4-1, 3.4-3）

4. [x] 概览 Tab：岗位字段可编辑 + 时间轴（只读数据渲染）
   - 目标：拆分 `OverviewTab` 与 `Timeline` 组件，先实现只读渲染与本地过滤 UI
   - 子项：
     - 4.1 [x] `OverviewTab` 表单 UI：字段绑定与"保存"按钮占位，不触发写入（Req 3.5-1）
     - 4.2 [x] `timeline.tsx`：渲染倒序事件（status/custom/note），移除筛选功能简化UI（Req 3.5-2, 3.5-3）
     - 4.3 [x] 纯函数：事件排序（按 created_at 倒序）；单元测试（Req 3.5-2, 3.5-3）
     - 4.4 [x] 可视化验收：概览显示可编辑字段与"保存"按钮；时间轴默认倒序展示；Modal滚动正常工作（Req 3.5-1, 3.5-2, 3.5-3）

5. [x] 简历 Tab：关联简历只读渲染与空态入口
   - 目标：实现 `ResumesTab` 只读列表与空态"生成定制简历"按钮占位（尚不接入写入）
   - 子项：
     - 5.1 [x] 读取 `getResumesByJobId`（若无，使用现有 `resumes` actions 组合查询），网格渲染（Req 3.6-1）
     - 5.2 [x] 空态按钮占位并冒泡到父层处理（Req 3.6-2, 3.6-3）
     - 5.3 [x] 可视化验收：有简历时显示卡片网格并可点击"打开/下载"；无简历时显示"生成定制简历"空态按钮（Req 3.6-1, 3.6-2）

6. [x] 笔记 Tab：大编辑器 + 笔记记录（只读）
   - 目标：实现 `NotesTab` 输入框 UI 与笔记记录只读列表（倒序）
   - 子项：
     - 6.1 [x] 顶部多行输入与"保存/取消/可选时间" UI；笔记记录只读渲染（Req 3.7-1, 3.7-3）
     - 6.2 [x] 纯函数：从日志中过滤 note_add/note_remove 的最终可见集合；单元测试（Req 3.7-2, 3.7-3）
     - 6.3 [x] 可视化验收：顶部多行编辑器与"保存/取消/时间"控件可交互；下方笔记记录列表可见（Req 3.7-1, 3.7-3）

7. [x] Server Actions 契约与最小实现（写入）：阶段/自定义阶段/笔记/事件时间
   - 目标：在 `src/utils/actions/jobs/actions.ts` 内新增最小写入 actions；保持 schema 不变
   - 子项：
     - 7.1 [x] `setSystemStage(jobId, from, to, created_at?)`：更新 `application_status` + 追加 `status_changed`（Req 3.3-2）
     - 7.2 [x] `addCustomStage(jobId, name, created_at?)` / `removeCustomStage(jobId, name, created_at?)`（Req 3.3-3）
     - 7.3 [x] `addNote(jobId, id, note, created_at?)` / `softDeleteNote(jobId, id, created_at?)`（Req 3.7-1, 3.7-3）
     - 7.4 [x] `updateEventTime(jobId, matcher, created_at)`（Req 3.5-4, 3.7-4）
     - 7.5 [x] 单元/集成测试：每个 action 在 JSON 合并与 RLS 场景的正确性；失败返回错误消息（Req 3.9-1, 3.12-1）
     - 7.6 [x] 可体验验收：运行测试套件看到所有相关用例通过的终端报告（或失败信息），作为阶段性可体验产出（Req 3.9-1）

8. [x] 接线：前端组件接入写入 actions（含乐观更新/回滚）
   - 目标：将阶段选择器、自定义阶段、笔记、时间修改与 Server Actions 对接
   - 子项：
     - 8.1 [x] 阶段选择器：单选切换→调用 `setSystemStage`；乐观更新；失败回滚与 Toast（Req 3.3-2, 3.9-1）
     - 8.2 [x] 自定义阶段：增删→对应 actions；集合本地重算；失败回滚与提示（Req 3.3-3, 3.9-1）
     - 8.3 [x] 笔记：保存→`addNote`；删除→`softDeleteNote`；保存后概览时间轴即时可见（Req 3.7-1, 3.7-2, 3.7-3）
     - 8.4 [x] 时间编辑：调用 `updateEventTime`；失败回滚（Req 3.5-4, 3.7-4）
     - 8.5 [x] 可视化验收：上述四类操作均触发 UI 立即变化（阶段高亮/标签集合/时间轴项/事件时间），并弹出成功/失败 Toast（Req 3.9-1）

9. [x] 概览 Tab：岗位字段保存对接
   - 目标：新增 `updateJobFields(jobId, partialJob)` 写入并接线
   - 子项：
     - 9.1 [x] Server Action：实现部分字段更新（Req 3.5-1）
     - 9.2 [x] 前端表单接线与成功/失败提示（Req 3.5-1, 3.9-1）
     - 9.3 [x] 可视化验收：点击"保存"后出现成功 Toast 且表单值为最新；失败弹出错误 Toast 并回滚（Req 3.5-1, 3.9-1）

10. [ ] 简历 Tab：接入生成与查看
   - 目标：集成 `tailorResumeToJob` → `createTailoredResume(job_id)` 流程与导航
   - 子项：
     - 10.1 [ ] 无关联简历空态按钮：选择基础简历→调用现有 AI 流程→绑定 `job_id` 并跳转编辑器（Req 3.6-2）
     - 10.2 [ ] 已有关联简历：主按钮“查看关联简历”→打开最新一份或列表供选择；“再生成一份”入口（Req 3.6-3）
     - 10.3 [ ] 可视化验收：空态点击“生成定制简历”后自动跳转到简历编辑器；已有简历点击“查看关联简历”可打开查看（Req 3.6-2, 3.6-3）

11. [ ] 错误处理与可达性
   - 目标：统一轻量提示与键盘可达性验证
   - 子项：
     - 11.1 [ ] Toast/Alert 统一封装；所有写入失败路径接入（Req 3.9-1）
     - 11.2 [ ] 焦点环与键盘操作路径校验（阶段控件、Tabs、保存、分页）（Req 3.10-1, 3.10-2）
     - 11.3 [ ] 设计令牌合规检查：
       - 品牌蓝仅用于 CTA/选中/焦点/加载（禁止卡片/背景/次要按钮）
       - 不得硬编码颜色，使用设计令牌/Tailwind 类（引用 `docs/DESIGN_TOKENS_USAGE.md`）
       - 可视化验收：通过视觉对照清单逐项截图或手动检查（Req 3.10-2；Design §10）
     - 11.3 [ ] 可视化验收：键盘可在阶段控件、Tabs、保存、分页间移动并触发；焦点环为品牌蓝（Req 3.10-1, 3.10-2）

12. [ ] 端到端测试（使用 MCP 工具 chrome-mcp）
   - 目标：覆盖关键用户旅程（不涉及部署与人工测试）
   - 子项：
     - 12.1 [ ] 列表筛选/分页 → 卡片阶段切换 → 详情 Modal → 笔记新增/软删/改时间（Req 3.1, 3.2, 3.3, 3.4, 3.7）
     - 12.2 [ ] 简历空态生成 → 跳转编辑器；已有简历查看/再生成（Req 3.6）
     - 12.3 [ ] 可体验验收：在终端看到 E2E 测试全部通过的报告（或失败信息）

13. [ ] 可选扩展：服务端阶段筛选与时间轴分页（保持 MVP 关闭）
   - 目标：为后续启用预留稳定接口与最小实现占位
   - 子项：
     - 13.1 [ ] `getJobListings` 增加可选 `application_status` 过滤参数（默认关闭）；测试（Open Question 1）
     - 13.2 [ ] 时间轴“加载更多”分页 API 契约与前端占位触点（Open Question 3）
     - 13.3 [ ] 可视化验收：开启实验开关后，顶栏出现“阶段（服务端）”选择并生效；时间轴底部显示“加载更多”占位按钮

---

执行说明：
- 每步均以小 PR/提交推进；优先完成功能最核心路径（2→7→8），随后补全表单与简历接线（9→10）。
- 测试优先级：纯函数单测 > Server Actions 集成 > 端到端。

---

## 📊 当前进度状态
**总进度：9/13 任务完成 (69%)**

### ✅ 已完成任务 (Task 1-9)
1. **前端页面骨架与阶段过滤** _(Task 1)_
   - 页面布局：采用 `container max-w-6xl mx-auto` 保持与 `/resumes` 一致的宽度
   - 阶段筛选：实现本地 `useState` 管理的前端过滤，支持"全部/申请/笔试/面试/谈判/录用/拒绝"
   - 添加申请按钮：占位实现，集成点预留
   - 单元测试：创建了阶段过滤和自定义阶段计算的纯函数测试

2. **阶段选择器组件** _(Task 2)_
   - UI组件：基于 Shadcn UI 的 Popover + RadioGroup，支持受控状态
   - 自定义阶段：实现了从 `application_log` 计算自定义阶段集合的纯函数
   - 时间编辑：提供事件时间修改入口（占位）
   - 单元测试：覆盖日志事件序列计算的边界用例

3. **详情 Modal 及 Tabs 容器** _(Task 3)_
   - Modal容器：实现聚焦捕获、ESC关闭、可达性支持
   - 顶部区域：岗位标题@公司 + 收起态阶段控件 + 操作按钮
   - Tabs结构：概览/简历/笔记/攻略四个标签页，带内状态保持

4. **概览 Tab：岗位字段可编辑 + 时间轴** _(Task 4)_
   - OverviewTab表单：完整的岗位信息编辑表单，包含所有字段和智能保存状态
   - Timeline组件：简洁的时间轴展示，按时间倒序排列，支持多种事件类型
   - 滚动优化：修复Modal和Tab容器的滚动问题，确保长表单内容完全可访问
   - 纯函数测试：`sortEventsByTime`函数的完整单元测试覆盖

### 🔧 设计合规修正
- **设计令牌系统严格遵循**：按照 `DESIGN_TOKENS_USAGE.md` 要求
  - 85% 黑白：主要内容使用 `text-black` 和 `bg-white`
  - 5% 灰色：辅助信息使用 `text-gray-600/500`、`border-gray-300`
  - 10% 品牌蓝：仅在CTA按钮、焦点状态、链接中使用
- **Shadcn UI 原生风格**：移除过度自定义样式，使用组件库默认variants
- **筛选器功能修正**：
  - 添加"全部"选项恢复完整筛选功能
  - 文案一致性修正（"办公方式" vs "职位类型"）
  - 阶段枚举匹配（"申请" vs "申请中"）
- **无障碍访问修正**：
  - 修复DialogContent缺少DialogTitle的错误
  - 移除SelectItem空值选项，确保组件合规
- **用户体验优化**：
  - 移除时间轴筛选功能，简化界面减少认知负担
  - 修复Modal滚动问题，确保长表单内容完全可访问

### 🎯 当前进度更新

#### ✅ **Task 5 完全完成** - 简历Tab优化版
- **功能实现**：完整的关联简历管理系统
  - 新增 `getResumesByJobId` Server Action
  - 响应式简历卡片网格（小屏1列，中屏2列，大屏3列）
  - 点击卡片直接打开简历编辑器
  - 空态引导用户生成定制简历
- **UI优化**：现代化卡片设计
  - 简洁的标题+类型+日期布局
  - 微妙的悬停动画效果
  - 完美的比例和间距
  - `yyyy/MM/dd` 日期格式

#### ✅ **Task 6 完全完成** - 笔记Tab大编辑器与笔记记录
- **笔记Tab UI组件** (`NotesTab`):
  - 简化界面：移除"笔记内容"标签文字，聚焦编辑体验
  - 智能时间选择器：显示当前时间，支持精确的年月日时分设置
  - 笔记记录管理：只读列表展示，支持删除操作
  - 完整交互流程：保存/取消/确认的用户友好操作
- **核心算法**：`getActiveNotesFromLog` 纯函数
  - 软删除逻辑：正确处理 note_add/note_remove 事件序列
  - 时间排序：按时间倒序显示最新笔记
  - 边界处理：空日志、无效字段、重复操作等场景
- **单元测试覆盖**：`notes-utils.test.ts`
  - 基本功能：提取、排序、内容保留
  - 软删除逻辑：添加/删除序列、重新添加处理
  - 边界用例：空数据、无效时间戳、数据完整性
- **集成接口**：完整的回调体系
  - JobDetailsModal：支持笔记增删回调
  - JobListingsCard：预留Server Actions接入点

#### ✅ **详情弹框布局优化**
- **简化的两行布局结构**：
  - 第一行：职位标题 @ 公司名称
  - 第二行：操作按钮组（状态选择器、添加笔记、生成定制简历）左对齐
  - **移除第三行**：删除地点和薪酬标签，减少视觉干扰
- **交互优化**：按钮组紧跟岗位信息，符合用户操作习惯

#### ✅ **Task 7 完全完成** - Server Actions 契约与最小实现
- **核心Server Actions实现**：7个完整的后端数据处理函数
  - `setSystemStage` - 系统阶段变更（申请→笔试→面试→谈判→录用→拒绝）
  - `addCustomStage/removeCustomStage` - 自定义阶段管理
  - `addNote/softDeleteNote` - 笔记新增和软删除
  - `updateEventTime` - 事件时间修改（支持复杂匹配器）
  - `updateJobFields` - 岗位字段部分更新
- **完整的类型安全**：新增类型定义与接口统一
  - `SystemStage` - 系统阶段枚举类型
  - `ApplicationLogEvent` - 申请日志事件接口
  - `EventMatcher` - 事件匹配器接口
- **全面测试覆盖**：19个单元测试用例
  - 覆盖所有Server Actions的正常流程
  - 边界条件和错误处理测试
  - Mock Supabase客户端和认证流程
- **技术验收通过**：可体验的验收测试脚本
  - 所有必需函数和类型定义检查通过
  - 代码质量检查（linting）通过
  - 构建兼容性修复（TypeScript类型错误）

#### ✅ **Task 8 完全完成** - 前端组件接入写入actions（含乐观更新/回滚）
- **完整的乐观更新系统**：`JobListingsCard`中实现了完整的状态管理
  - `optimisticUpdates` Map：管理所有pending的UI更新
  - `applyOptimisticUpdate/clearOptimisticUpdate`：应用和清除乐观更新
  - `getJobWithOptimisticUpdate`：获取合并了乐观更新的job数据
- **阶段选择器完整对接**：系统阶段和自定义阶段统一管理
  - 系统阶段切换：调用`setSystemStage`，支持乐观更新和错误回滚
  - 自定义阶段管理：`addCustomStage/removeCustomStage`完整流程
  - 智能阶段检测：`getCurrentActualStage`从日志中获取真实当前阶段
  - 加载状态管理：状态变更时显示加载动画（300ms最小持续）
- **笔记系统完整实现**：新增、删除、时间轴同步
  - 笔记添加：`addNote`调用，UUID生成，乐观更新
  - 笔记删除：`softDeleteNote`调用，即时UI反馈
  - 时间轴同步：概览Tab中笔记变更立即可见
  - 保存后标签页保持：修复了保存后自动跳转overview的问题
- **时间编辑功能**：完整的内联编辑体验
  - 内联编辑器：在时间轴项目下方展开，不遮挡内容
  - 完整时间选择：年月日时分全覆盖，使用`useMemo`优化性能
  - 服务器同步：调用`updateEventTime`，支持复杂事件匹配
  - 取消/确认：用户友好的操作流程
- **统一错误处理和用户反馈**：
  - Toast通知：使用`sonner`提供成功/失败反馈
  - 响应式设计：Toast宽度适配不同屏幕尺寸
  - 错误回滚：所有操作失败时自动回滚UI状态
  - 加载指示：所有异步操作都有相应的loading状态
- **UI/UX优化细节**：
  - 智能图标系统：系统阶段使用语义化图标，自定义阶段使用红色星星
  - 流畅的状态转换：所有操作都有即时的视觉反馈
  - 数据一致性：乐观更新确保UI和服务器数据保持同步

#### ✅ **Task 9 完全完成** - 概览Tab岗位字段保存对接
- **Server Actions完整实现**：`updateJobFields` 函数完全可用
  - 支持部分字段更新（职位、公司、地点、类型、薪资、描述等）
  - 完整的参数验证和错误处理
  - 数据库原子操作确保一致性
- **前端集成完成**：JobListingsCard 中完整实现保存流程
  - `handleJobFieldsUpdate` 函数处理所有保存逻辑
  - 乐观更新：保存前立即更新UI状态
  - Toast反馈：成功显示绿色提示，失败显示红色错误信息
  - 数据同步：保存后自动更新本地状态和Modal数据
- **OverviewTab优化**：表单状态管理和用户体验
  - 智能按钮状态：有更改时可用，无更改时禁用
  - 加载状态：保存时显示"保存中..."
  - 数据同步：切换岗位时自动重置表单到最新数据
- **完整验收通过**：
  - ✅ 编辑字段后保存按钮变为可用
  - ✅ 点击保存显示加载状态和成功Toast
  - ✅ 保存后数据持久化到数据库
  - ✅ 失败时显示错误Toast并保持原状态
  - ✅ 列表页和Modal数据实时同步

### 🎯 下一步计划
**核心功能路径**（按优先级排序）：
- **Task 10**：简历Tab集成 - 连接现有的简历生成流程
  - 无关联简历空态：集成 `tailorResumeToJob` → `createTailoredResume` 流程
  - 已有关联简历：实现查看和再生成功能
  - 跳转编辑器：完成生成后自动跳转到简历编辑页面

**扩展功能**（后续开发）：
- **Task 11**：错误处理与可达性优化
- **Task 12**：端到端测试覆盖

### 🎯 **重要里程碑达成**
✅ **MVP核心交互功能完全可用**：
- 用户可以完整管理岗位申请流程（阶段切换、自定义阶段、笔记、时间修改）
- 所有数据变更都会持久化到数据库
- 完整的错误处理和用户反馈系统
- 乐观更新确保流畅的用户体验


