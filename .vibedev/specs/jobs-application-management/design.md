### 岗位申请管理 MVP — 设计文档

#### 1. 概览（Overview）
- **目标**: 在 `/(dashboard)/jobs` 实现极简岗位申请管理（阶段=状态，单层中央 Modal），时间轴默认可见，笔记独立 Tab；复用现有 AI（JD 解析、定制简历绑定 `job_id`）。
- **约束**: 不改表结构与 RLS；最少改动复用现有组件与 actions；写入失败轻量提示；无撤销。
- **关键交互流**:
  - 列表页：中文筛选（工作地点/职位类型/阶段[前端过滤]）+ 分页；卡片右上角“阶段 ▼”展开选择器；点卡片主体打开详情 Modal。
  - 详情 Modal：Tabs=概览（岗位字段可编辑 + 时间轴）｜简历（已关联卡/空态+生成）｜笔记（大编辑器+历史）｜攻略（占位）。
  - 阶段选择器：系统阶段单选写入 `application_status` 并追加 `status_changed`；下半区自定义阶段增删事件；任一事件支持“✎时间”。

---

#### 2. 架构（Architecture）
- **前端**: Next.js App Router + 客户端组件（`ui/select`、`ui/tabs` 等），小范围新增组合组件；状态以本地 `useState` 与轻量乐观更新为主。
- **服务端**: Next Server Actions + Supabase（RLS 生效），在不改表的前提下新增最小写入类 actions（仅契约，不涉及实现细节）。
- **数据**: 表 `jobs` 提供 `application_status` 与 `application_log(jsonb)`；表 `resumes` 通过 `job_id` 关联。

```mermaid
sequenceDiagram
  autonumber
  actor U as 用户
  participant L as 列表/卡片
  participant S as 阶段选择器
  participant M as 详情Modal
  participant A as Server Actions
  participant DB as Supabase

  U->>L: 筛选/翻页
  L->>A: getJobListings(filters,page)
  A->>DB: SELECT jobs
  DB-->>A: rows
  A-->>L: jobs + pagination

  U->>S: 展开阶段选择器 / 切换阶段
  S->>A: setSystemStage(jobId, from, to, created_at?)
  A->>DB: UPDATE jobs.application_status + append application_log
  DB-->>A: ok
  A-->>S: ok

  U->>M: 打开详情Modal
  M->>A: 可选 getJobById/getResumesByJobId
  A->>DB: SELECT
  DB-->>A: rows
  A-->>M: job + resumes

  U->>M: 添加笔记/自定义阶段/改时间
  M->>A: addNote/removeNote/addCustomStage/removeCustomStage/updateEventTime
  A->>DB: UPDATE jobs.application_log
  DB-->>A: ok
  A-->>M: ok

  U->>M: 生成定制简历
  M->>A: tailorResumeToJob + createTailoredResume(job_id)
  A->>DB: INSERT resumes
  DB-->>A: new resume
  A-->>M: navigate to editor
```

---

#### 3. 组件与接口（Components and Interfaces）

3.1 前端组件结构
- `src/app/(dashboard)/jobs/page.tsx`
  - 组合现有 `JobListingsCard`（保留原行为），外层增加“阶段”前端过滤与“添加申请”入口按钮（调用 `formatJobListing` → `createJob` 流程）。
- `src/components/jobs/job-listings-card.tsx`
  - 复用现有筛选与分页（工作地点/职位类型），保留 UI；为最少改动，阶段筛选控件与状态保留在外层页面，由页面在传入数据前进行前端过滤。
- 新增（建议新文件，避免侵入式修改）：
  - `src/components/jobs/stage-selector.tsx`：阶段选择器（系统阶段单选 + 自定义阶段列表 + “+ 添加” + ✎时间）。
  - `src/components/jobs/job-details-modal.tsx`：单层中央 Modal，包含 Tabs 与顶部固定区（标题、阶段控件、主按钮、添加笔记）。
    - `OverviewTab`：岗位字段表单 + 时间轴（含筛选“全部/阶段/自定义/笔记”）。
    - `ResumesTab`：`resumes.job_id=job.id` 的卡片网格；空态 + “生成定制简历”。
    - `NotesTab`：大编辑器（保存/取消/可选时间） + 笔记记录（仅 note 类事件）。
    - `GuideTab`：占位说明。
  - `src/components/jobs/timeline.tsx`：根据 `application_log` 渲染倒序时间轴，支持本地过滤与“✎时间”。

3.2 前端状态与交互要点
- 乐观更新：阶段切换、自定义阶段增删、笔记新增/软删、事件时间修改均先更新本地 UI，再依据服务端结果确认或回滚。
- 前端阶段筛选：对当前列表数据集进行本地过滤（不触发额外请求）。
- 可达性：`ui/select`、`ui/tabs`、`ui/button` 均可聚焦；Modal 捕获焦点；按 Esc 关闭（除非有未保存的笔记编辑）。

3.3 Server Actions（接口契约，伪接口，保持最小集）
- `updateJobFields(jobId, partialJob)`
  - 入参：`jobId:string`，`partialJob: { position_title?, company_name?, location?, employment_type?, work_location?, job_url?, salary_range?, description? }`
  - 效果：只更新提供的字段；返回最新 `job`。

- `setSystemStage(jobId, from, to, created_at?)`
  - 入参：`jobId:string`，`from:'申请'|'笔试'|'面试'|'谈判'|'录用'|'拒绝'|null`，`to:同枚举`，`created_at?:ISO8601`
  - 效果：更新 `application_status=to`；在 `application_log` 末尾追加 `{type:'status_changed', from, to, created_at}`。

- `addCustomStage(jobId, name, created_at?)` / `removeCustomStage(jobId, name, created_at?)`
  - 入参：`jobId:string`，`name:string`，`created_at?:ISO8601`
  - 效果：分别追加 `{type:'custom_stage_add', name, created_at}` / `{type:'custom_stage_remove', name, created_at}`。

- `addNote(jobId, id, note, created_at?)` / `softDeleteNote(jobId, id, created_at?)`
  - 入参：`jobId:string`，`id:string`（由前端生成 uuid），`note:string`，`created_at?:ISO8601`
  - 效果：分别追加 `{type:'note_add', id, note, created_at}` / `{type:'note_remove', id, created_at}`。

- `updateEventTime(jobId, matcher, created_at)`
  - 入参：`jobId:string`，`matcher: { type: 'status_changed' | 'custom_stage_add' | 'custom_stage_remove' | 'note_add' | 'note_remove', id?: string, name?: string, from?: string, to?: string, created_at?: string }`，`created_at:ISO8601`
  - 效果：在 `application_log` 中定位首个与 `matcher` 匹配的事件，只更新其 `created_at`（无其他变更）。
  - 说明：为保持“不改表”，非笔记事件无 `id`；采用匹配器（类型 + 关键字段）定位；如存在重复项，按首次匹配更新。

（读取）
- `getJobById(jobId)`：返回单条 `job`（含 `application_status`、`application_log`）。
- `getResumesByJobId(jobId)`：返回 `resumes[]`。

---

#### 4. 数据模型（Data Models）
- `jobs.application_status`: `'申请'|'笔试'|'面试'|'谈判'|'录用'|'拒绝'|null`（中文枚举，与文档一致）。
- `jobs.application_log: jsonb[]` 事件形态：
  - 阶段变更：`{ type:'status_changed', from:string|null, to:string, created_at:string }`
  - 自定义阶段：`{ type:'custom_stage_add'|'custom_stage_remove', name:string, created_at:string }`
  - 笔记：`{ type:'note_add', id:string, note:string, created_at:string }` / `{ type:'note_remove', id:string, created_at:string }`

- “当前自定义阶段集合”计算：遍历日志，按 `name` 聚合 `add/remove` 得出存在集。
- 时间轴过滤：基于 `type`（status/custom/note）做本地筛选。

---

#### 5. 错误处理（Error Handling）
- 轻量提示：所有写入失败通过 Toast/Alert 告知，不提供撤销。
- 乐观更新回滚：
  - 阶段切换/自定义阶段/笔记/时间修改：失败即回滚本地变更并提示。
- 权限/认证失败：提示“未登录或无权限”，阻止本地状态提交。
- 数据一致性：Server Action 内部以 JSON 原子更新方式处理 `application_log`，避免并发覆盖；必要时先 SELECT 最新日志再合并。

---

#### 6. 测试策略（Testing Strategy）
- 单元测试（前端纯函数）：
  - 日志→时间轴的归并/排序/过滤函数。
  - 自定义阶段集合的计算函数。
  - 阶段/类型中文映射与展示格式化（含相对时间格式）。

- 集成测试（Server Actions 层）：
  - `setSystemStage`：状态更新与日志追加。
  - `addCustomStage/removeCustomStage`：成对追加与集合计算正确性。
  - `addNote/softDeleteNote`：笔记追加与软删事件。
  - `updateEventTime`：匹配器更新目标事件时间且仅影响单条。

- 端到端（E2E，Playwright）：
  - 列表筛选与分页。
  - 卡片“阶段 ▼”切换与即时反馈。
  - 详情 Modal 打开/切 Tab/保存岗位字段。
  - 笔记新增→概览时间轴即时可见；软删与改时间。
  - 简历 Tab 空态→“生成定制简历”→跳转编辑器；已有简历→“查看关联简历”。

- 可达性检查：
  - 键盘导航路径覆盖阶段选择器、Tabs、保存、分页；焦点环与可视状态。

---

#### 7. 设计决策与理由（Decisions & Rationales）
- 不改表，仅以 `application_log` 记录行为：最小风险、可回溯、便于扩展统计。
- 阶段中文枚举直写 `application_status`：满足直观显示与前端过滤需要。
- 非笔记事件不引入 `id`：保持表外 JSON 简洁；时间修改通过匹配器定位，满足 MVP。
- 阶段筛选前端化：减少服务端改动与复杂度；如后期有数据量压力，再补服务端 where 参数。
- 组件新增采用“并列新增”而非侵入式改造：减少对现有 `JobListingsCard` 的影响，符合“最少改动”。

---

#### 8. 研究要点与结论（Research & Findings）
- 现有代码确认：
  - `schema.sql` 已含 `application_status` 与 `application_log` 字段与索引。
  - 现有 actions：`createJob/deleteJob/getJobListings/createEmptyJob`、`tailorResumeToJob`、`createTailoredResume` 可直接复用。
  - 现有组件：`job-listings-card.tsx` 已具备筛选与分页能力，可作为列表主体复用。
- 设计结论：在不改表前提下，上述最小新增 actions 满足所有 MVP 写入路径；前端以新增组件组合，避免破坏既有样式与行为。

---

#### 9. 开放问题（Open Questions）
- 是否需要追加服务端阶段筛选以利于大数据量分页？
- 事件“匹配器”是否需更严格（如加入旧 `created_at`）以降低误匹配概率？
- 时间轴是否需要“加载更多/分页”支持？

---

#### 10. 视觉与设计规范引用（Design System Compliance）
- 规范来源：`docs/DESIGN_TOKENS_USAGE.md`、`CLAUDE.md`

> 引用 @DESIGN_TOKENS_USAGE.md（节选）
>
> - 设计理念：85% 黑白 + 5% 灰色 + 10% 品牌蓝（L5）
> - 品牌蓝使用场景（10% 使用比例）：主要 CTA、链接文字、选中状态、焦点环、加载指示器、激活状态（L161–170）
> - 禁止使用品牌蓝的场景：背景色、卡片背景、次要按钮、装饰元素、普通文本（除链接）（L171–177）
> - 禁止硬编码颜色，必须使用设计令牌/Tailwind 类（L259–263）

> 引用 @CLAUDE.md（节选）
>
> - 样式系统：Tailwind + Shadcn UI（New York 主题），响应式与 Framer Motion 动画（L93–99）
> - 设计令牌系统：统一管理颜色/间距/圆角/阴影等变量，类型安全（L98–101）
> - 开发注意事项：必须使用设计令牌与既有 UI 组件；焦点环与颜色比例需符合规范（L102–110）

落地约束（本功能层面）：
- 颜色比例与用途
  - 品牌蓝仅用于：
    - 顶部主 CTA（例如“生成定制简历”、关键提交按钮）
    - 阶段选择器中“当前选中态”的视觉强调与焦点环
    - Tabs/分页的“选中或聚焦”状态与加载指示
  - 禁止在：卡片/Modal 背景、二级按钮、装饰性渐变等处使用品牌蓝；背景与组件主体以黑白为主，灰用量≤5%。
- 组件实现
  - 统一复用 `src/components/ui/*`（Shadcn）与 Tailwind 实用类，不引入自定义硬编码色值。
  - 焦点可达：所有交互组件需具备可见的品牌蓝焦点环；键盘导航覆盖阶段控件、Tabs、保存、分页。
- Token 与样式实现建议
  - 若需内联样式，必须经由 `design-tokens` 工具函数或 Tailwind 变量间接取得，不得硬编码十六进制颜色。
  - 动画与高光效果以阴影令牌和透明度处理为主，避免品牌蓝大面积铺底。

审查清单（交付验收）：
- 检查所有新组件是否仅在允许场景使用品牌蓝；焦点环是否为品牌蓝；是否存在任何硬编码颜色；灰度与蓝色使用比例是否符合 5%/10% 上限。
- 检查 `Modal/Tabs/StageSelector/Timeline` 的选中与焦点状态是否采用设计令牌或 Tailwind 令牌映射。

参考文档：
- `docs/DESIGN_TOKENS_USAGE.md`
- `CLAUDE.md`

---

请审阅本设计文档。若确认无误，我将进入“任务规划”阶段，输出任务拆解与优先级（仅文本，不涉及实现）。


