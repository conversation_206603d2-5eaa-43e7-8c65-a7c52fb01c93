### 岗位申请管理 MVP — 需求说明（EARS）

#### 0. 简介
- **目标**: 在 `/(dashboard)/jobs` 提供极简岗位申请管理，围绕“阶段=状态”与“单层中央 Modal”完成管理闭环；复用现有 AI（JD 解析、定制简历并绑定 `job_id`）；时间轴默认可见；笔记在独立 Tab 简洁编辑。
- **范围**: 前端页面、交互与最小写入接口契约（不改表）；列表页筛选与分页；详情 Modal（概览｜简历｜笔记｜攻略占位）；阶段选择器（系统阶段+自定义阶段）。
- **非目标**: 不做多层路由拦截、不做撤销/历史版本、不做富文本笔记、不做高级搜索与统计、不改数据库表结构或 RLS 策略。

#### 1. 术语与枚举
- 系统阶段（写入 `jobs.application_status`）: `'申请'|'笔试'|'面试'|'谈判'|'录用'|'拒绝'`。 
- 申请日志（写入 `jobs.application_log` JSON 数组）：
  - 阶段变更: `{ type:'status_changed', from, to, created_at }`
  - 自定义阶段: `{ type:'custom_stage_add'|'custom_stage_remove', name, created_at }`
  - 笔记: `{ type:'note_add', id, note, created_at }` / `{ type:'note_remove', id, created_at }`

#### 2. 角色
- 作为普通用户，我管理自己的岗位与与之关联的定制简历。
- 作为 Pro 用户，我可复用现有 AI 能力更高限额与速度（沿用既有策略与限流）。

---

#### 3. 需求列表（用户故事 + EARS 验收标准）

3.1 列表页（/jobs）筛选与分页
- 用户故事: 作为用户，我希望按中文筛选与分页查看岗位卡，以便快速定位与管理。
- 验收标准（EARS）:
  1) 当页面首次加载时，系统应按创建时间倒序展示岗位卡，默认每页 6 条，并显示“上一页/下一页”。
  2) 当我在顶栏选择“工作地点（远程/现场/混合）”时，系统应据此筛选岗位并更新展示。
  3) 当我在顶栏选择“职位类型（全职/兼职/实习/合同/Co-op）”时，系统应据此筛选岗位并更新展示。
  4) 当我在顶栏选择“阶段（全部/申请/笔试/面试/谈判/录用/拒绝）”时，系统应在前端对已加载集合进行筛选并更新展示。
  5) 若任一筛选无匹配结果，系统应展示空态与清除筛选入口（或回到“全部”）。

3.2 卡片结构与快捷入口
- 用户故事: 作为用户，我希望在卡片上快速查看关键信息并能直接切换阶段。
- 验收标准（EARS）:
  1) 当卡片渲染时，应展示“职位标题 @ 公司、地点、职位类型、办公形态、更新时间（相对时间）与最多 3 个关键词”。
  2) 当我点击卡片主体时，系统应打开“详情（单层中央 Modal）”。
  3) 当我点击卡片右上角“阶段 ▼”时，系统应展开“阶段选择器”，支持切换系统阶段与管理自定义阶段。

3.3 阶段选择器（系统阶段+自定义阶段）
- 用户故事: 作为用户，我希望在同一弹层内既能看到当前进度，也能切换系统阶段与维护自定义阶段。
- 验收标准（EARS）:
  1) 当我展开阶段选择器时，应显示上半区系统阶段纵向单选与下半区自定义阶段列表与“+ 添加阶段”输入。
  2) 当我点击系统阶段 A→B 时，系统应更新 `jobs.application_status=B` 并在 `application_log` 追加 `{type:'status_changed', from:A, to:B, created_at}`。
  3) 当我添加自定义阶段 X 时，系统应在 `application_log` 追加 `{type:'custom_stage_add', name:X, created_at}`；当我删除 X 时，应追加 `{type:'custom_stage_remove', name:X, created_at}`。
  4) 当我点击“✎时间”修改任一事件时间时，系统应仅更新该事件 `created_at` 值。
  5) 当写入失败时，系统应以轻量提示（Toast/Alert）告知；不提供撤销。

3.4 详情弹框（中央 Modal，单层）
- 用户故事: 作为用户，我希望在不离开列表的前提下完成岗位查看、编辑与关联内容管理。
- 验收标准（EARS）:
  1) 当我从卡片进入详情时，顶部应显示“标题 @ 公司 + 阶段控件（收起态显示当前阶段与简短进度）+ 主按钮 + 添加笔记按钮（跳转笔记 Tab 并聚焦）”。
  2) 当岗位无关联简历时，主按钮应显示“生成定制简历”；当已有简历时，主按钮应显示“查看关联简历”，并在简历 Tab 内保留“再生成一份”。
  3) 当我在详情内切换 Tabs 时，系统应在不关闭 Modal 的前提下切换对应内容：概览（默认）｜简历｜笔记｜攻略（占位）。

3.5 概览 Tab（岗位字段可编辑 + 时间轴默认可见）
- 用户故事: 作为用户，我希望编辑岗位字段并查看完整事件时间轴（含笔记只读摘要）。
- 验收标准（EARS）:
  1) 当我编辑职位、公司、地点、职位类型、办公形态、URL、薪资、描述并点击“保存”时，系统应仅更新相应字段并提示成功/失败。
  2) 当我进入概览时，应默认展示“时间轴（倒序，最新在上）”，包含阶段变更、自定义阶段、笔记只读摘要等事件。
  3) 当我在时间轴顶部选择“全部/阶段/自定义阶段/笔记”时，系统应在本地对事件列表进行过滤并即时更新。
  4) 当我点击某事件的“✎时间”时，系统应支持修改该事件 `created_at`（仅该事件）。
  5) 当我在笔记类事件上点击“删除”时，系统应追加 `{type:'note_remove', id, created_at}`，在 UI 上将该笔记标记为已删除（软删）。

3.6 简历 Tab（关联简历与再生成）
- 用户故事: 作为用户，我希望查看与该岗位关联的定制简历，必要时一键生成并进入编辑。
- 验收标准（EARS）:
  1) 当岗位存在 `resumes.job_id = 当前岗位.id` 的记录时，系统应以卡片形式展示并提供“打开/下载”。
  2) 当不存在关联简历时，应显示空态并提供“生成定制简历”，触发：选择基础简历 → `tailorResumeToJob` → `createTailoredResume(job_id=当前岗位.id)` → 跳转简历编辑器。
  3) 当已有关联简历时，主按钮文案应变为“查看关联简历”，并在本 Tab 内提供“再生成一份”入口，复用相同流程生成新定制简历并同样绑定 `job_id`。

3.7 笔记 Tab（大编辑器 + 笔记记录）
- 用户故事: 作为用户，我希望以简洁的方式记录和回顾岗位相关笔记。
- 验收标准（EARS）:
  1) 当我在顶部多行文本编辑器输入内容并点击“保存”时，系统应在 `application_log` 追加 `{type:'note_add', id, note, created_at(可选/默认现在)}` 并清空输入框。
  2) 当我保存笔记后，概览 Tab 的时间轴应即时出现该笔记只读摘要（倒序可见）。
  3) 当我在笔记记录列表点击“删除”时，系统应追加 `{type:'note_remove', id, created_at}` 并在 UI 上隐藏或标注为已删除（软删）。
  4) 当我在笔记记录上点击“✎时间”时，系统应支持仅修改该笔记事件 `created_at`。

3.8 攻略 Tab（占位）
- 用户故事: 作为用户，我希望了解“即将上线”内容与保留 CTA 占位。
- 验收标准（EARS）:
  1) 当我打开攻略 Tab 时，应展示“即将上线”的简短说明与 CTA 占位，不触发任何跳转或写入操作。

3.9 错误处理与提示
- 用户故事: 作为用户，我希望在失败情况下得到明确但轻量的反馈。
- 验收标准（EARS）:
  1) 当任意写入失败时，系统应以轻量提示方式告知（Toast/Alert），不提供撤销入口。
  2) 当网络异常或权限不足时，系统应提示并避免 UI 进入不一致状态（如回滚切换的阶段高亮）。

3.10 可达性与视觉
- 用户故事: 作为用户，我希望关键操作可通过键盘完成，并保持品牌一致的视觉焦点。
- 验收标准（EARS）:
  1) 当使用键盘导航时，阶段选择器、Tabs、保存按钮与分页控件应可聚焦并可通过键盘激活。
  2) 焦点环应使用品牌蓝；灰阶≤5%，品牌蓝≤10% 用于主 CTA/焦点/选中（遵循设计令牌）。

3.11 性能与分页
- 用户故事: 作为用户，我希望在岗位较多时仍然获得流畅体验。
- 验收标准（EARS）:
  1) 当翻页或筛选变更时，系统应在合理时间内完成查询渲染（一般≤1.5s，网络环境可变）。
  2) 当“阶段”筛选为前端过滤时，系统应确保在当前页集合上即时生效，不触发额外请求；其他筛选按照现有实现可走服务端过滤。

3.12 安全与数据一致性
- 用户故事: 作为用户，我希望我的数据仅我可见且写入安全。
- 验收标准（EARS）:
  1) 当进行任何读写时，应遵循现有 Supabase RLS，限制为 `auth.uid()` 的数据行。
  2) 当删除定制简历时（现有逻辑），若涉及关联岗位的副作用，应维持与当前实现一致的再验证与路径失效策略。

---

#### 4. 技术与接口约束（不改表，仅最小写入契约）
- 新增最小 Actions（契约，不写实现）：
  - `updateJobFields(jobId, partialJob)`：更新职位/公司/地点/职位类型/办公形态/URL/薪资/描述任意子集。
  - `setSystemStage(jobId, from, to, created_at?)`：更新 `application_status=to` 并在 `application_log` 追加 `status_changed` 事件。
  - `addCustomStage(jobId, name, created_at?)` / `removeCustomStage(jobId, name, created_at?)`：在 `application_log` 追加对应事件。
  - `addNote(jobId, id, note, created_at?)` / `softDeleteNote(jobId, id, created_at?)`：追加 `note_add` / `note_remove` 事件。
  - `updateEventTime(jobId, eventIdOrMatcher, created_at)`：仅更新该事件的 `created_at`。
- 列表读取沿用 `getJobListings`；若需要服务端阶段筛选，可在不改表前提下追加可选 `application_status` 过滤参数（当前 MVP 要求为前端过滤，可暂不增加）。

---

#### 5. 验收要点（面向 UAT）
1) 顶栏筛选皆为中文，无搜索；分页正常可用。
2) 卡片“阶段 ▼”可直接切换系统阶段；同一弹层管理自定义阶段增删与时间修改。
3) 详情 Modal：
   - 概览可编辑岗位字段并保存；
   - 简历 Tab 展示 `job_id` 关联简历；无简历时主按钮“生成定制简历”，有简历时主按钮“查看关联简历”，且可“再生成一份”；
   - 笔记 Tab 提供大编辑器，保存后概览时间轴即时显示只读摘要；
   - 攻略 Tab 为占位说明；
   - 时间轴默认可见、倒序、含笔记只读；事件可改时间；笔记可软删。
4) 所有写入失败均有轻量提示；不提供撤销。
5) 键盘可达；焦点环使用品牌蓝，遵循设计令牌与现有 `ui/*` 组件风格。

---

#### 6. 开放问题（若后续扩展）
- 是否需要在服务端支持“阶段”筛选以优化大列表性能？（当前按 MVP=前端过滤）
- 自定义阶段是否需要可排序或说明文字？（当前按事件集构建集合，无排序）
- 时间轴项需不需要“加载更多”（分页）？（当前可滚动加载或按需增量）

---

请审阅以上需求文档。若无异议，我将进入“设计文档”阶段，输出信息架构、组件划分、状态流与接口契约细化（仅文字与伪接口）。


