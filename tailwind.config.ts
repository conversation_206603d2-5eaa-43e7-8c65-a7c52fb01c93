import type { Config } from "tailwindcss";
import { TAILWIND_COLORS } from "./src/config/colors";

const config = {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	extend: {
  		colors: {
  			// 使用CSS变量，支持主题切换
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			// 极简黑白主题 - 使用新的设计令牌
  			white: TAILWIND_COLORS.white,
  			black: TAILWIND_COLORS.black,
  			gray: TAILWIND_COLORS.gray,
  			blue: TAILWIND_COLORS.blue,
  			red: TAILWIND_COLORS.red,
  		},
  		borderRadius: {
  			none: 'var(--radius-none)',
  			sm: 'var(--radius-sm)',
  			md: 'var(--radius-md)',
  			lg: 'var(--radius-lg)',
  			xl: 'var(--radius-xl)',
  			'2xl': 'var(--radius-2xl)',
  			'3xl': 'var(--radius-3xl)',
  			full: '9999px',
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
  			blob: {
  				'0%': {
  					transform: 'translate(0px, 0px) scale(1)'
  				},
  				'33%': {
  					transform: 'translate(30px, -50px) scale(1.1)'
  				},
  				'66%': {
  					transform: 'translate(-20px, 20px) scale(0.9)'
  				},
  				'100%': {
  					transform: 'translate(0px, 0px) scale(1)'
  				}
  			},
  			'loading-dot': {
  				'0%': {
  					opacity: '0.2',
  					transform: 'translateX(-2px) scale(0.8)'
  				},
  				'50%': {
  					opacity: '0.8',
  					transform: 'translateX(2px) scale(1)'
  				},
  				'100%': {
  					opacity: '0.2',
  					transform: 'translateX(-2px) scale(0.8)'
  				}
  			},
  			shine: {
  				'0%': {
  					'background-position': '0% 0%'
  				},
  				'50%': {
  					'background-position': '100% 100%'
  				},
  				to: {
  					'background-position': '0% 0%'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down var(--duration-fast) ease-out',
  			'accordion-up': 'accordion-up var(--duration-fast) ease-out',
  			blob: 'blob var(--duration-slower) infinite',
  			shine: 'shine var(--duration-normal) infinite linear'
  		},
  		boxShadow: {
  			none: 'var(--shadow-none)',
  			sm: 'var(--shadow-sm)',
  			md: 'var(--shadow-md)',
  			lg: 'var(--shadow-lg)',
  			xl: 'var(--shadow-xl)',
  			'2xl': 'var(--shadow-2xl)',
  		},
  		spacing: {
  			px: 'var(--spacing-px)',
  			0: 'var(--spacing-0)',
  			1: 'var(--spacing-1)',
  			2: 'var(--spacing-2)',
  			3: 'var(--spacing-3)',
  			4: 'var(--spacing-4)',
  			5: 'var(--spacing-5)',
  			6: 'var(--spacing-6)',
  			7: 'var(--spacing-7)',
  			8: 'var(--spacing-8)',
  			9: 'var(--spacing-9)',
  			10: 'var(--spacing-10)',
  			11: 'var(--spacing-11)',
  			12: 'var(--spacing-12)',
  			14: 'var(--spacing-14)',
  			16: 'var(--spacing-16)',
  			20: 'var(--spacing-20)',
  			24: 'var(--spacing-24)',
  			32: 'var(--spacing-32)',
  			40: 'var(--spacing-40)',
  			48: 'var(--spacing-48)',
  			56: 'var(--spacing-56)',
  			64: 'var(--spacing-64)',
  		},
  		typography: {
  			xxxs: {
  				css: {
  					fontSize: '0.625rem',
  					h1: {
  						fontSize: '1rem'
  					},
  					h2: {
  						fontSize: '0.875rem'
  					},
  					h3: {
  						fontSize: '0.75rem'
  					},
  					h4: {
  						fontSize: '0.625rem'
  					}
  				}
  			},
  			xxs: {
  				css: {
  					fontSize: '0.75rem',
  					h1: {
  						fontSize: '1.25rem'
  					},
  					h2: {
  						fontSize: '1.15rem'
  					},
  					h3: {
  						fontSize: '1rem'
  					},
  					h4: {
  						fontSize: '0.875rem'
  					}
  				}
  			}
  		}
  	}
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/container-queries"),
    require("@tailwindcss/typography")
  ],
} satisfies Config;

export default config;
