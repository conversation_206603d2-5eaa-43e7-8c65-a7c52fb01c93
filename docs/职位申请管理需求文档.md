### /jobs 岗位申请管理 MVP 需求文档

#### 1. 概述
- 目标：在 `/(dashboard)/jobs` 提供极简岗位申请管理。阶段=状态，使用同一个“阶段选择器”（既显示进度也可编辑）。时间轴默认可见。笔记在独立 Tab 内以简单文本编辑。
- 约束：不改数据结构与后端能力；复用现有 AI（JD 解析、定制简历并绑定 `job_id`）。单层“中央 Modal”，桌面与移动一致心智。

---

#### 2. 信息架构与路由
- 路由：`/(dashboard)/jobs` 列表页；详情以中央 Modal 打开（不做拦截直达）。
- 查询参数（前端过滤）：`工作地点`、`职位类型`、`阶段`。

---

#### 3. 列表页（/jobs）
- 顶栏筛选（中文，无搜索）：
  - 工作地点（远程｜现场｜混合）
  - 职位类型（全职｜兼职｜实习｜合同）
  - 阶段（全部｜申请｜笔试｜面试｜谈判｜录用｜拒绝）
  - 添加申请（粘贴 JD 一键解析，通添加定制简历中的流程）
- 栅格与分页：桌面5列/平板3列/移动1列；底部“上一页/下一页”。
- 卡片结构与交互：
  - 行1：职位标题 @ 公司
  - 行2：地点｜职位类型｜办公形态｜更新时间
  - 右上角：阶段选择器按钮“阶段 ▼”（点开即可切换系统阶段；下半区管理自定义阶段）
  - 点击卡片主体：打开详情 Modal

```
┌───────────────────────────────────────────────────────────────────────────────┐
│ 筛选： [工作地点 ▼]  [职位类型 ▼]  [阶段 ▼]                    [添加申请 ▸]      │
├───────────────────────────────────────────────────────────────────────────────┤
│ ┌───────────────────────────┐  ┌───────────────────────────┐  ┌─────────────┐ │
│ │ 标题 @ 公司               │  │ 标题 @ 公司               │  │ 标题 @ 公司  │ │
│ │ 地点 · 职位类型 · 形态 · 更新时间 │  │ 地点 · 职位类型 · 形态 · 更新时间 │  │ …           │ │
│ │                                     │  │                                     │ │
│ │                          [阶段 ▼]   │  │                          [阶段 ▼]   │ │
│ └───────────────────────────┘  └───────────────────────────┘  └─────────────┘ │
├───────────────────────────────────────────────────────────────────────────────┤
│ 分页： « 上一页   1  2  3   下一页 »                                           │
└───────────────────────────────────────────────────────────────────────────────┘
```

---

#### 4. 阶段选择器（阶段=状态，选择=进度显示，二者合一）
- 系统阶段（固定、单选，写入 `jobs.application_status`）：申请｜笔试｜面试｜谈判｜录用｜拒绝。
- 展开态结构：
  - 上半区：纵向进度节点（可选单选，点击即切换阶段并记录 `status_changed`）。
  - 下半区：自定义阶段（已有列表；每项可删除；“+ 添加阶段”单行输入）。
  - 任一事件可“✎时间”修改该事件的 `created_at`，增删以事件写入日志。

```
[阶段 ▼]
┌───────────────────────────────────────────┐
│ 系统阶段（纵向单选=进度）                   │
│ ● 申请     [✎时间]                         │
│ ○ 笔试     [✎时间]                         │
│ ○ 面试     [✎时间]                         │
│ ○ 谈判     [✎时间]                         │
│ ○ 录用     [✎时间]                         │
│ ○ 拒绝     [✎时间]                         │
│ ───────────────────────────────────────    │
│ 自定义阶段                                │
│  [+ 添加阶段︱__________ ]  [确定]          │
└───────────────────────────────────────────┘
```

---

#### 5. 详情弹框（中央 Modal，单层）
- 顶部固定区：
  - 标题 @ 公司
  - 阶段选择器（与卡片一致；收起态显示当前阶段+简短进度）
  - 主按钮（根据是否有关联简历切换）：
    - 无关联简历：显示“生成定制简历”
    - 已有关联简历：不显示“生成定制简历”
  - 次按钮：“添加笔记”（切到“笔记”Tab 并聚焦编辑器）
- Tabs：概览（默认）｜简历｜笔记｜攻略（占位）

```
┌════════════════════════ 岗位详情（Modal） ════════════════════════┐
│ 标题 @ 公司   [阶段控件(收起)]   [主按钮]   [添加笔记]   [建议]      │
│ Tabs:  [概览]*   简历   笔记   攻略(占位)                          │
├───────────────────────────────────────────────────────────────────┤
│ （当前 Tab 内容区域）                                              │
└───────────────────────────────────────────────────────────────────┘
```

---

#### 6. 各 Tab 规格
- 概览（默认 Tab）
  - 岗位字段可编辑：职位、公司、地点、职位类型、办公形态、URL、薪资、描述；[保存]
  - 时间轴（默认可见、倒序、最新在上；含笔记只读摘要——长文折叠；事件可“✎时间”；笔记可删除［软删］）
  - 顶部筛选：全部｜阶段｜自定义阶段｜笔记（本地过滤）

```
┌─ 岗位信息（可编辑） ─────────────────────────────────────────────┐
│ …字段…  [保存]                                                   │
└─────────────────────────────────────────────────────────────────┘
┌─ 时间轴（默认可见，倒序；含笔记只读） ───────────────────────────┐
│ 筛选： [全部] [阶段] [自定义阶段] [笔记]                           │
│ • 10:32  笔记：联系HR……                        [✎时间] [删除]      │
│ • 09:10  阶段变更：笔试 → 面试                  [✎时间]            │
│ • 08:55  自定义阶段：添加「技术测评」            [✎时间]            │
│ …（加载更多）                                                     │
└─────────────────────────────────────────────────────────────────┘
```

- 简历
  - 展示 `resumes.job_id = 当前岗位.id` 的简历卡（打开编辑（调整））。
  - 空态：提示“暂无关联简历”＋“生成定制简历”按钮。

```
┌─ 已关联简历 ─────────────────────────────────────────────────────┐
│ ┌───────────┐  ┌───────────┐  …                                  │
│ │ 简历名称   │  │ 简历名称   │  [打开][下载]                 │
│ └───────────┘  └───────────┘                                     │
│ 空态：暂无 → [生成定制简历]                                        │
└─────────────────────────────────────────────────────────────────┘
```

- 笔记
  - 顶部：简洁大编辑器（多行文本；[保存/取消]；可选“时间”，默认现在）。
  - 下方：笔记记录卡片（仅笔记类事件，倒序；支持删除［软删］）。
  - 保存后：在“概览”时间轴即时出现该笔记的只读项。

```
┌─ 新增笔记 ───────────────────────────────────────────────────────┐
│ [ 多行文本编辑区（纯文本） ]                                      │
│ [🕒时间(可选)]                     [取消]   [保存]                 │
└─────────────────────────────────────────────────────────────────┘
┌─ 笔记记录（倒序） ────────────────────────────────────────────────┐
│ • 10:32  笔记：……                               [✎时间] [删除]     │
│ • 08:21  笔记：……                               [✎时间] [删除]     │
└─────────────────────────────────────────────────────────────────┘
```

- 攻略（占位）
  - 展示“即将上线”说明；保留 CTA 占位（当前无跳转逻辑）。

---

#### 7. AI 复用
- 添加申请：手动或粘贴 JD → `formatJobListing` 预填 → 确认 → `createJob`。
- 生成定制简历：选基础简历 → `tailorResumeToJob` → `createTailoredResume`（绑定 `job_id`）→ 跳转简历编辑器。
- 已有简历时，主按钮切换为“查看关联简历”，并保留“再生成一份”入口。

---

#### 8. 数据与事件（不改表）
- `jobs.application_status`：'申请'|'笔试'|'面试'|'谈判'|'录用'|'拒绝'
- `jobs.application_log`（jsonb）：
  - 阶段变更：`{ type:'status_changed', from, to, created_at }`
  - 自定义阶段：`{ type:'custom_stage_add'|'custom_stage_remove', name, created_at }`
  - 笔记：`{ type:'note_add', id, note, created_at }` / `{ type:'note_remove', id, created_at }`
- 修改时间：仅更新该事件的 `created_at`。

---

#### 9. 验收要点
- 顶栏筛选为中文，无搜索；分页正常。
- 卡片“阶段 ▼”可直接切换系统阶段；自定义阶段在同一弹层增删。
- 详情 Modal：
  - 概览可编辑岗位字段；
  - 简历 Tab 展示关联简历；无简历时主按钮“生成定制简历”，有简历时主按钮“查看关联简历”，并可“再生成一份”。
  - 笔记 Tab 提供大编辑器，保存后时间轴即刻显示只读摘要。
  - 攻略 Tab 为占位。
  - 时间轴默认可见、倒序、含笔记只读；事件可改时间；笔记可软删。
- 所有写入失败展示轻量提示；不提供撤销。

---

#### 10. 视觉与可达性
- 遵循设计令牌：黑白主色、灰≤5%、品牌蓝≤10%（主 CTA/焦点/选中）。
- 组件：`ui/select`、`ui/radio-group`、`ui/textarea`、`ui/button`、`ui/card`、`ui/tabs`、`ui/separator`、`ui/skeleton`。
- 键盘可达；焦点环使用品牌蓝。