# 设计令牌系统使用说明

## 概述

SnapOffer 项目现在使用了统一的设计令牌系统，专注于浅色模式的极简扁平设计。设计理念是 **85% 黑白 + 5% 灰色 + 10% 品牌蓝**。

## 文件结构

```
src/
├── lib/
│   └── design-tokens.ts          # 核心设计令牌定义
├── config/
│   └── colors.ts                 # 颜色配置文件（使用设计令牌）
├── app/
│   └── globals.css               # CSS变量定义
└── tailwind.config.ts            # Tailwind配置（使用设计令牌）
```

## 设计令牌类型

### 1. 颜色令牌

```typescript
// 基础色
designTokens.colors.white      // #ffffff
designTokens.colors.black      // #000000

// 灰色系 (5% 使用比例)
designTokens.colors.gray.50    // #f9f9f9
designTokens.colors.gray.100   // #f5f5f5
designTokens.colors.gray.200   // #e5e5e5
designTokens.colors.gray.300   // #d4d4d4
designTokens.colors.gray.400   // #a3a3a3
designTokens.colors.gray.500   // #737373
designTokens.colors.gray.600   // #525252
designTokens.colors.gray.700   // #404040
designTokens.colors.gray.800   // #262626
designTokens.colors.gray.900   // #171717

// 品牌蓝 (10% 使用比例)
designTokens.colors.blue.50    // #eff6ff
designTokens.colors.blue.100   // #dbeafe
designTokens.colors.blue.200   // #bfdbfe
designTokens.colors.blue.300   // #93c5fd
designTokens.colors.blue.400   // #60a5fa
designTokens.colors.blue.500   // #3b82f6
designTokens.colors.blue.600   // #2563eb
designTokens.colors.blue.700   // #1d4ed8
designTokens.colors.blue.800   // #1e40af
designTokens.colors.blue.900   // #1e3a8a

// 功能色 (极少使用)
designTokens.colors.red.500    // #ef4444
designTokens.colors.green.500  // #22c55e
designTokens.colors.yellow.500 // #eab308
```

### 2. 间距令牌

```typescript
designTokens.spacing.px    // '1px'
designTokens.spacing.0     // '0'
designTokens.spacing.1     // '0.25rem' (4px)
designTokens.spacing.2     // '0.5rem' (8px)
designTokens.spacing.3     // '0.75rem' (12px)
designTokens.spacing.4     // '1rem' (16px)
designTokens.spacing.5     // '1.25rem' (20px)
designTokens.spacing.6     // '1.5rem' (24px)
designTokens.spacing.8     // '2rem' (32px)
designTokens.spacing.10    // '2.5rem' (40px)
designTokens.spacing.12    // '3rem' (48px)
designTokens.spacing.16    // '4rem' (64px)
```

### 3. 圆角令牌

```typescript
designTokens.borderRadius.none   // '0'
designTokens.borderRadius.sm     // '0.125rem' (2px)
designTokens.borderRadius.md     // '0.25rem' (4px)
designTokens.borderRadius.lg     // '0.5rem' (8px)
designTokens.borderRadius.xl     // '0.75rem' (12px)
designTokens.borderRadius['2xl']  // '1rem' (16px)
designTokens.borderRadius['3xl']  // '1.5rem' (24px)
designTokens.borderRadius.full   // '9999px'
```

### 4. 阴影令牌

```typescript
designTokens.shadows.none  // 'none'
designTokens.shadows.sm    // '0 1px 2px 0 rgb(0 0 0 / 0.05)'
designTokens.shadows.md    // '0 4px 6px -1px rgb(0 0 0 / 0.1)'
designTokens.shadows.lg    // '0 10px 15px -3px rgb(0 0 0 / 0.1)'
designTokens.shadows.xl    // '0 20px 25px -5px rgb(0 0 0 / 0.1)'
designTokens.shadows['2xl'] // '0 25px 50px -12px rgb(0 0 0 / 0.25)'
```

## 使用方法

### 1. 在 TypeScript/JavaScript 中使用

```typescript
import { designTokens } from '@/lib/design-tokens';

// 使用颜色
const primaryColor = designTokens.colors.blue[500];
const backgroundColor = designTokens.colors.white;

// 使用间距
const padding = designTokens.spacing[4]; // '1rem'

// 使用圆角
const borderRadius = designTokens.borderRadius.md; // '0.25rem'

// 使用阴影
const boxShadow = designTokens.shadows.md;
```

### 2. 在 Tailwind CSS 中使用

```tsx
'use client';

// 颜色类（更新：按钮悬停不改变背景颜色）
<div className='bg-white text-black border-gray-200'>
  <button className='bg-blue-500 text-white'>
    Click me
  </button>
</div>

// 间距类
<div className='p-4 m-2 space-y-4'>
  <div className='mb-2'>Content</div>
</div>;

// 圆角类
<div className='rounded-md p-2'>rounded-md</div>
<div className='rounded-lg p-2'>rounded-lg</div>
<div className='rounded-xl p-2'>rounded-xl</div>
<div className='rounded-full p-2'>rounded-full</div>;

// 阴影类
<div className='shadow-md p-2'>shadow-md</div>
<div className='shadow-lg p-2'>shadow-lg</div>
<div className='shadow-xl p-2'>shadow-xl</div>;
```

### 3. 在 Shadcn UI 组件中使用

#### Button 组件
```tsx
'use client';

import { Button } from '@/components/ui/button';

// 主要 CTA 按钮（更新：悬停不改变背景色，移除 hover:bg-*）
<Button className="bg-blue-500 text-white">
  主要操作
</Button>

// 次要按钮（更新：悬停不改变背景色）
<Button variant="secondary" className="bg-gray-100">
  次要操作
</Button>

// 危险操作（更新：悬停不改变背景色）
<Button variant="destructive" className="bg-red-500">
  删除操作
</Button>
```

#### Card 组件
```tsx
'use client';

import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';

// 标准卡片 - 使用设计令牌
<Card className="bg-white border-gray-200 shadow-md rounded-lg">
  <CardHeader>
    <CardTitle className="text-black text-xl">卡片标题</CardTitle>
  </CardHeader>
  <CardContent>
    <p className="text-gray-600">卡片内容</p>
  </CardContent>
</Card>

// 强调卡片 - 更强阴影
<Card className="bg-white border-gray-100 shadow-lg rounded-xl">
  <CardHeader>
    <CardTitle className="text-black text-2xl">重要信息</CardTitle>
  </CardHeader>
  <CardContent>
    <p className="text-gray-700">重要内容</p>
  </CardContent>
</Card>
```

#### Input 组件
```tsx
'use client';

import { Input } from '@/components/ui/input';

// 标准输入框
<Input 
  className="border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
  placeholder="请输入内容"
/>

// 错误状态
<Input 
  className="border-red-500 rounded-md focus:border-red-500 focus:ring-red-500"
  placeholder="错误状态"
/>

// 禁用状态
<Input 
  disabled
  className="border-gray-200 rounded-md bg-gray-50"
  placeholder="禁用状态"
/>
```

#### Dialog 组件
```tsx
'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

<Dialog>
  <DialogTrigger asChild>
    {/* 更新：移除 hover:bg-* */}
    <Button className="bg-blue-500 text-white">打开对话框</Button>
  </DialogTrigger>
  <DialogContent className="bg-white border-gray-200 rounded-xl shadow-xl">
    <DialogHeader>
      <DialogTitle className="text-black text-xl">对话框标题</DialogTitle>
      <DialogDescription className="text-gray-600">
        对话框描述内容
      </DialogDescription>
    </DialogHeader>
    <div className="text-black">
      对话框内容
    </div>
  </DialogContent>
</Dialog>
```

#### Select 组件
```tsx
'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

<Select>
  {/* 更新：按钮取消焦点可视。输入类组件可按需保留轻量 focus 样式。*/}
  <SelectTrigger className="border-gray-300 rounded-md">
    <SelectValue placeholder="请选择" />
  </SelectTrigger>
  <SelectContent className="bg-white border-gray-200 rounded-md shadow-md">
    <SelectItem value="option1" className="text-black hover:bg-gray-50">选项 1</SelectItem>
    <SelectItem value="option2" className="text-black hover:bg-gray-50">选项 2</SelectItem>
    <SelectItem value="option3" className="text-black hover:bg-gray-50">选项 3</SelectItem>
  </SelectContent>
</Select>
```

### 4. 在 CSS 中使用

```css
.my-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-radius: var(--radius-md);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-md);
}
```

### 5. 实际使用场景示例

#### 简历编辑器组件
```tsx
'use client';

import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export function ResumeEditor() {
  return (
    <div className="space-y-6">
      {/* 标题区域 */}
      <Card className="bg-white border-gray-200 shadow-md rounded-lg">
        <CardHeader>
          <CardTitle className="text-black text-xl">简历编辑器</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Input 
              className="border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
              placeholder="姓名"
            />
            <Input 
              className="border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
              placeholder="职位"
            />
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex space-x-4">
        <Button className="bg-blue-500 text-white">
          保存简历
        </Button>
        <Button variant="secondary" className="bg-gray-100">
          预览
        </Button>
        <Button variant="outline" className="border-gray-300">
          导出PDF
        </Button>
      </div>
    </div>
  );
}
```

#### 岗位申请卡片
```tsx
'use client';

import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export function JobApplicationCard() {
  return (
    <Card className="bg-white border-gray-200 shadow-md rounded-lg hover:shadow-lg transition-shadow duration-300">
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-black text-lg">前端开发工程师</CardTitle>
          <Badge className="bg-blue-100 text-blue-800">进行中</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <p className="text-gray-600">公司名称 • 北京 • 全职</p>
          <p className="text-gray-500 text-sm">申请时间：2024-01-15</p>
          
          <div className="flex space-x-2">
            <Button className="bg-blue-500 text-white">
              查看详情
            </Button>
            <Button variant="secondary" className="bg-gray-100">
              编辑笔记
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

#### AI 对话界面
```tsx
'use client';

import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export function AIChatInterface() {
  return (
    <div className="space-y-4">
      {/* AI 回复 */}
      <Card className="bg-gray-50 border-gray-200 rounded-lg">
        <CardContent className="p-4">
          <p className="text-black">
            根据您的简历，我建议您在面试中重点突出您的项目经验...
          </p>
        </CardContent>
      </Card>

      {/* 用户输入 */}
      <div className="flex space-x-2">
        {/* 输入控件可按需保留轻量 focus 提示（不使用按钮样式） */}
        <Input 
          className="flex-1 border-gray-300 rounded-md"
          placeholder="输入您的问题..."
        />
        <Button className="bg-blue-500 text-white">
          发送
        </Button>
      </div>
    </div>
  );
}
```

## 颜色使用规则

### 品牌蓝使用场景（10% 使用比例）

✅ **允许使用品牌蓝的场景：**
- 主要CTA按钮
- 链接文字
- 选中状态
- 加载指示器
- 激活状态

❌ **禁止使用品牌蓝的场景：**
- 背景色
- 卡片背景
- 次要按钮
- 装饰元素
- 普通文本（除链接外）

### 使用比例限制

- **黑白**: 85%
- **灰色**: 5%
- **品牌蓝**: 10%
- **功能色**: 极少使用

## 工具函数

```typescript
import { designTokenUtils } from '@/lib/design-tokens';

// 获取颜色
const color = designTokenUtils.getColor('blue.500');

// 获取间距
const spacing = designTokenUtils.getSpacing('4');

// 获取圆角
const radius = designTokenUtils.getBorderRadius('md');

// 获取阴影
const shadow = designTokenUtils.getShadow('lg');

// 检查颜色使用是否符合规范
const isValid = designTokenUtils.validateColorUsage('primary-cta-button', 'blue.500');

// 生成透明度变体
const colorWithOpacity = designTokenUtils.withOpacity('#3b82f6', 0.5);
```

## 迁移指南

### 从硬编码颜色迁移

**之前：**
```tsx
'use client';

<div style={{ backgroundColor: '#3b82f6', padding: '16px' }}>
  <button style={{ borderRadius: '8px' }}>Click</button>
</div>;
```

**之后：**
```tsx
'use client';

<div className='bg-blue-500 p-4'>
  <button className='rounded-md'>Click</button>
</div>;
```

### 从自定义样式迁移

**之前：**
```css
.custom-button {
  background-color: #3b82f6;
  color: #ffffff;
  border-radius: 8px;
  padding: 8px 16px;
}
```

**之后：**
```css
.custom-button {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-4);
}
```

## 最佳实践

### 1. 组件开发最佳实践
```tsx
// ✅ 好的实践 - 使用设计令牌和语义化类名
export function MyComponent() {
  return (
    <Card className="bg-white border-gray-200 shadow-md rounded-lg">
      <CardHeader>
        <CardTitle className="text-black text-lg">组件标题</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600">组件内容</p>
        <Button className="bg-blue-500 text-white">
          操作按钮
        </Button>
      </CardContent>
    </Card>
  );
}

// ❌ 避免的实践 - 硬编码颜色和不一致的样式
export function MyComponent() {
  return (
    <div style={{ backgroundColor: '#ffffff', border: '1px solid #e5e5e5', borderRadius: '8px' }}>
      <h3 style={{ color: '#000000', fontSize: '18px' }}>组件标题</h3>
      <p style={{ color: '#6b7280' }}>组件内容</p>
      <button style={{ backgroundColor: '#3b82f6', color: '#ffffff' }}>
        操作按钮
      </button>
    </div>
  );
}
```

### 2. 响应式设计最佳实践
```tsx
// ✅ 使用响应式断点
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">卡片 1</Card>
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">卡片 2</Card>
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">卡片 3</Card>
</div>

// ✅ 响应式间距
<div className="space-y-4 sm:space-y-6 lg:space-y-8">
  <div>内容 1</div>
  <div>内容 2</div>
  <div>内容 3</div>
</div>
```

### 3. 状态管理最佳实践
```tsx
// ✅ 使用设计令牌定义状态样式
const getStatusStyles = (status: string) => {
  switch (status) {
    case 'success':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'error':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'warning':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

<Badge className={getStatusStyles('success')}>成功</Badge>
```

### 4. 动画和过渡最佳实践
```tsx
// ✅ 使用设计令牌定义动画
<div className="transition-all duration-300 ease-in-out hover:shadow-lg hover:-translate-y-1">
  <Card className="bg-white border-gray-200 shadow-md rounded-lg">
    卡片内容
  </Card>
</div>

// ✅ 统一的悬停效果（更新：使用位移/阴影反馈，不改变背景色）
<Button className="bg-blue-500 text-white transition-transform duration-200 hover:-translate-y-0.5">
  悬停效果
</Button>
```

### 5. 布局最佳实践
```tsx
// ✅ 使用 Flexbox 和 Grid 布局
<div className="flex flex-col space-y-4">
  <div className="flex justify-between items-center">
    <h2 className="text-black text-xl">标题</h2>
    <Button className="bg-blue-500 text-white">
      操作
    </Button>
  </div>
  <div className="grid grid-cols-1 gap-4">
    {items.map(item => (
      <Card key={item.id} className="bg-white border-gray-200 shadow-md rounded-lg">
        {item.content}
      </Card>
    ))}
  </div>
</div>
```

## 注意事项

### 1. 设计令牌使用规范
- **必须使用设计令牌**：所有颜色、间距、圆角、阴影都必须通过设计令牌管理
- **禁止硬编码**：不允许在代码中直接使用颜色值、尺寸值等
- **遵循比例**：严格控制品牌蓝的使用比例（10%）
- **保持一致**：所有组件使用统一的设计令牌和样式规范

### 2. 性能优化
- **优先使用 Tailwind 类**：避免自定义 CSS，使用 Tailwind 的实用类
- **合理使用动画**：只在必要时使用动画，避免过度使用
- **优化响应式设计**：使用移动端优先的断点系统

### 3. 可访问性
- **确保对比度**：文字和背景的对比度必须符合 WCAG 2.1 AA 标准
- **键盘导航**：确保所有交互元素都可以通过键盘访问
- **屏幕阅读器**：使用语义化标签和 ARIA 属性

### 4. 开发规范
- **组件命名**：使用 PascalCase 命名组件文件
- **类名组织**：按照功能、布局、状态的顺序组织类名
- **注释规范**：在复杂组件中添加必要的注释

## 故障排除

### 1. 颜色显示问题
**问题**：设计令牌颜色不显示或显示异常

**解决方案**：
```tsx
// 检查导入
import { designTokens } from '@/lib/design-tokens';

// 检查 Tailwind 配置
// 确认 tailwind.config.ts 中正确配置了颜色

// 检查 CSS 变量
// 确认 app/globals.css 中定义了 CSS 变量
```

### 2. 类型错误问题
**问题**：TypeScript 报错设计令牌类型错误

**解决方案**：
```tsx
// 检查类型导入
import type { DesignTokens } from '@/lib/design-tokens';

// 使用正确的类型注解
const color: string = designTokens.colors.blue[500];

// 检查路径是否正确
const spacing = designTokens.spacing['4']; // 使用字符串索引
```

### 3. 样式不一致问题
**问题**：不同组件间的样式不一致

**解决方案**：
```tsx
// 创建共享样式常量
const commonCardStyles = "bg-white border-gray-200 shadow-md rounded-lg";
const commonButtonStyles = "bg-blue-500 hover:bg-blue-600 text-white";

// 使用共享样式
<Card className={commonCardStyles}>
  <Button className={commonButtonStyles}>按钮</Button>
</Card>
```

### 4. 响应式问题
**问题**：移动端显示异常

**解决方案**：
```tsx
// 使用移动端优先的断点
<div className="w-full sm:w-96 md:w-128 lg:w-160">
  响应式宽度
</div>

// 检查触摸目标大小
<Button className="min-h-[44px] min-w-[44px]">
  触摸按钮
</Button>
```

### 5. 构建问题
**问题**：构建时样式丢失

**解决方案**：
```bash
# 检查 Tailwind 配置
npm run tailwind:build

# 检查依赖
npm install

# 清理构建缓存
npm run clean
npm run build
```

## 常用工具函数

### 1. 颜色工具函数
```typescript
import { designTokenUtils } from '@/lib/design-tokens';

// 获取颜色
const primaryColor = designTokenUtils.getColor('blue.500');

// 验证颜色使用
const isValid = designTokenUtils.validateColorUsage('primary-cta-button', 'blue.500');

// 生成透明度变体
const colorWithOpacity = designTokenUtils.withOpacity('#3b82f6', 0.5);
```

### 2. 样式工具函数
```typescript
// 生成响应式类名
const getResponsiveClasses = (base: string, responsive: Record<string, string>) => {
  return Object.entries(responsive).reduce((classes, [breakpoint, className]) => {
    return `${classes} ${breakpoint}:${className}`;
  }, base);
};

// 使用示例
const cardClasses = getResponsiveClasses('bg-white border-gray-200', {
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
});
```

### 3. 状态工具函数
```typescript
// 获取状态样式
const getStatusColor = (status: string) => {
  const statusColors = {
    success: 'green',
    error: 'red',
    warning: 'yellow',
    info: 'blue',
  };
  return statusColors[status as keyof typeof statusColors] || 'gray';
};
```

---

这个设计令牌系统将帮助项目保持一致的视觉风格，提高开发效率，并确保设计规范的有效执行。通过遵循这些最佳实践和注意事项，可以创建出高质量、一致的用户界面。