{"permissions": {"allow": ["<PERSON><PERSON>(chmod:*)", "Bash(node scripts/color-replace.js)", "Bash(grep:*)", "Bash(npm run lint)", "Bash(node:*)", "mcp__chrome-mcp__chrome_navigate", "Bash(git add:*)", "mcp__specs-mcp__vibedev_specs_workflow_start", "mcp__specs-mcp__vibedev_specs_goal_confirmed", "mcp__specs-mcp__vibedev_specs_requirements_start", "mcp__specs-mcp__vibedev_specs_requirements_confirmed", "mcp__specs-mcp__vibedev_specs_design_start", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs"], "deny": []}}