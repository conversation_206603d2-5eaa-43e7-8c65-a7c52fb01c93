# 技术栈与构建系统

## 核心技术栈

### 前端框架
- **Next.js 15** - React框架，使用App Router架构
- **React 19** - 用户界面库
- **TypeScript** - 类型安全的JavaScript超集

### 样式系统
- **Tailwind CSS** - 原子化CSS框架
- **Shadcn UI** - 组件库，基于Radix UI，使用New York主题
- **Framer Motion** - 动画库
- **Lucide React** - 图标库
- **设计令牌系统** - 统一的设计变量管理（85%黑白+5%灰色+10%品牌蓝）

### 状态管理与数据
- **React Context** - 客户端状态管理
- **Server Actions** - 服务端数据处理
- **Zod** - 数据验证和模式定义

### 数据库与认证
- **Supabase** - PostgreSQL数据库 + 认证服务
- **Supabase Auth** - 用户认证系统
- **行级安全策略** - 数据访问控制

### AI集成
- **Vercel AI SDK** - 多AI提供商集成
  - OpenAI (GPT模型)
  - Anthropic (Claude)
  - Google (Gemini)
  - DeepSeek
  - Groq
  - OpenRouter (额外模型支持)
- **自定义AI工具** - 在 `src/lib/tools.ts` 和 `src/lib/prompts.ts` 中定义
- **AI功能** - 简历定制化、岗位匹配、STAR框架优化、速率限制
- **AI配置** - 支持不同用户等级的模型选择和功能限制

### 文档处理
- **@react-pdf/renderer** - PDF生成
- **html2pdf.js** - HTML转PDF
- **TipTap** - 富文本编辑器
- **PDF处理库** - pdf-parse, pdf.js-extract

### 支付系统
- **Stripe** - 支付处理
- **@stripe/react-stripe-js** - React Stripe集成

### 其他关键库
- **React Hook Form** - 表单处理和验证
- **React Dropzone** - 文件上传和拖拽
- **React Markdown** - Markdown渲染
- **Framer Motion** - 动画效果
- **Sonner** - Toast通知
- **OpenRouter AI SDK** - 额外的AI模型提供商支持
- **Redis (@upstash/redis)** - 缓存和速率限制
- **PDF处理库** - pdf-parse, pdf.js-extract, html2canvas
- **Markdown处理** - marked, remark-gfm, rehype-prism-plus
- **富文本编辑** - TipTap编辑器套件
- **虚拟滚动** - @tanstack/react-virtual
- **响应式面板** - react-resizable-panels

## 构建系统

### 开发环境
- **Node.js >= 20.0.0** - 运行时环境
- **Turbopack** - 开发构建工具（Next.js内置）
- **ESLint** - 代码质量检查
- **TypeScript** - 类型检查

### 构建配置
- **Next.js配置** (`next.config.ts`):
  - MDX支持
  - 页面扩展名：`.ts`, `.tsx`, `.mdx`
  - 生产环境源码映射关闭
  - React严格模式关闭
  - 代理配置支持
  - 环境变量处理

### 样式配置
- **Tailwind配置** (`tailwind.config.ts`):
  - 自定义颜色主题（极简黑白风格）
  - 自定义动画（blob, shine, loading-dot等）
  - 响应式设计
  - 容器查询支持
  - 排版插件支持
  - 动画插件支持
  - 设计令牌系统集成

- **设计令牌系统** (`src/lib/design-tokens.ts`):
  - 统一的颜色管理（黑白灰+品牌蓝）
  - 间距、圆角、阴影系统化定义
  - 严格的颜色使用比例控制
  - 工具函数和类型安全支持
  - 详细的迁移指南和最佳实践

### 组件配置
- **Shadcn UI配置** (`components.json`):
  - New York主题
  - TypeScript支持
  - CSS变量主题切换
  - 组件别名配置

## 常用开发命令

### 核心命令
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint

# 测试代理功能
npm run test:proxy
```

### 开发环境特殊配置
- 禁用TLS验证：`NODE_TLS_REJECT_UNAUTHORIZED=0`
- 禁用Node.js警告：`NODE_NO_WARNINGS=1`
- 使用Turbopack：`--turbopack`

## 项目约定

### 代码风格
- 使用TypeScript严格模式
- 遵循ESLint规则
- 优先使用Server Components
- 使用CSS变量进行主题切换

### 组件开发
- 优先使用Server Components
- 必要时使用Client Components
- 使用React Context进行状态管理
- 使用Server Actions处理数据操作

### 样式约定
- 使用Tailwind CSS类
- 遵循已有的设计系统
- 响应式设计，移动优先
- 使用自定义动画和过渡效果
- **设计令牌优先**：使用设计令牌系统管理所有设计变量
- **颜色比例控制**：严格遵循85%黑白+5%灰色+10%品牌蓝的使用比例
- **品牌蓝限制**：仅在主要CTA、链接、选中状态等关键场景使用品牌蓝
- **一致性保证**：所有组件使用统一的设计令牌，确保视觉一致性

### 文件组织
- 按功能模块组织代码
- 使用清晰的命名约定
- 组件文件使用 `.tsx` 扩展名
- 类型定义使用 `.ts` 扩展名

## 环境要求

### 运行时环境
- Node.js >= 20.0.0
- PostgreSQL数据库（通过Supabase）
- Stripe账户（支付功能）

### 开发工具
- VS Code（推荐）
- TypeScript支持
- ESLint插件
- Tailwind CSS插件