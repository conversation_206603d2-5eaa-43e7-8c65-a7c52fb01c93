# 项目结构与文件组织

## 目录结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── (dashboard)/       # 仪表板路由组（需要认证）
│   │   ├── home/          # 仪表板首页
│   │   ├── profile/       # 用户资料
│   │   ├── resumes/       # 简历管理
│   │   ├── settings/      # 设置页面
│   │   └── subscription/  # 订阅管理
│   ├── admin/             # 管理后台
│   ├── auth/              # 认证相关页面
│   ├── api/               # API 路由
│   ├── blog/              # 博客页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── auth/              # 认证组件
│   ├── resume/            # 简历编辑器组件
│   │   ├── assistant/     # AI助手组件
│   │   ├── editor/        # 简历编辑器
│   │   ├── management/    # 简历管理
│   │   └── shared/        # 共享组件
│   ├── dashboard/         # 仪表板组件
│   ├── landing/           # 首页组件
│   ├── ui/                # 基础UI组件 (Shadcn)
│   ├── layout/            # 布局组件
│   ├── profile/           # 用户资料组件
│   ├── settings/          # 设置组件
│   ├── jobs/              # 职位管理组件
│   ├── subscription/      # 订阅相关组件
│   ├── cover-letter/      # 求职信组件
│   ├── pricing/           # 价格展示组件
│   ├── waitlist/          # 等待列表组件
│   ├── theme/             # 主题组件
│   ├── magicui/           # 魔法UI组件
│   ├── navigation/        # 导航组件
│   ├── blog/              # 博客组件
│   └── shared/            # 共享组件
├── lib/                   # 工具库和配置
│   ├── pdf/               # PDF 生成相关
│   ├── prompts.ts         # AI 提示词模板
│   ├── tools.ts           # AI 工具函数
│   ├── types.ts           # TypeScript 类型定义
│   ├── utils.ts           # 通用工具函数
│   ├── schemas.ts         # 数据模式定义
│   ├── design-tokens.ts   # 设计令牌系统
│   ├── zod-schemas.ts     # Zod模式定义
│   ├── rateLimiter.ts     # 速率限制
│   ├── redis.ts           # Redis配置
│   ├── proxy.ts           # 代理配置
│   ├── cache/             # 缓存相关
│   ├── performance/       # 性能优化
│   ├── theme/             # 主题相关
│   └── website-templates/ # 网站模板
├── utils/                 # 实用工具函数
│   ├── actions/           # Server Actions
│   │   ├── jobs/          # 职位相关操作
│   │   ├── profiles/      # 用户资料操作
│   │   ├── resumes/       # 简历相关操作
│   │   ├── stripe/        # 支付相关操作
│   │   ├── subscriptions/ # 订阅相关操作
│   │   ├── cover-letter/  # 求职信相关操作
│   │   └── avatars/       # 头像相关操作
│   ├── auth.ts            # 认证工具
│   ├── supabase/          # Supabase 配置
│   ├── ai-tools.ts        # AI工具函数
│   └── auth-cache.ts      # 认证缓存
├── hooks/                 # React Hooks
├── config/                # 配置文件
│   └── colors.ts          # 颜色配置
└── types/                 # TypeScript 类型定义
```

## 关键文件位置

### 页面路由
- **首页**: `src/app/page.tsx`
- **仪表板**: `src/app/(dashboard)/home/<USER>
- **简历编辑**: `src/app/(dashboard)/resumes/[id]/page.tsx`
- **用户设置**: `src/app/(dashboard)/settings/page.tsx`
- **管理后台**: `src/app/admin/page.tsx`

### 核心组件
- **简历编辑器**: `src/components/resume/editor/`
- **AI助手**: `src/components/resume/assistant/`
- **简历管理**: `src/components/resume/management/`
- **认证组件**: `src/components/auth/`
- **UI组件**: `src/components/ui/`

### 配置文件
- **Next.js配置**: `next.config.ts`
- **Tailwind配置**: `tailwind.config.ts`
- **Shadcn配置**: `components.json`
- **颜色配置**: `src/config/colors.ts`

### 工具库
- **AI工具**: `src/lib/tools.ts`
- **AI提示词**: `src/lib/prompts.ts`
- **PDF生成**: `src/lib/pdf/`
- **类型定义**: `src/lib/types.ts`

### 数据操作
- **Server Actions**: `src/utils/actions/`
- **认证工具**: `src/utils/auth.ts`
- **Supabase客户端**: `src/utils/supabase/`

## 文件命名约定

### 组件文件
- 使用PascalCase：`ResumeEditor.tsx`, `UserProfile.tsx`
- 客户端组件使用 `.client.tsx` 后缀：`ResumeEditorClient.tsx`
- 布局组件使用 `layout.tsx`：`EditorLayout.tsx`

### 页面文件
- 使用 `page.tsx`：`home/page.tsx`
- 布局文件使用 `layout.tsx`：`resumes/[id]/layout.tsx`
- 加载状态使用 `loading.tsx`：`resumes/[id]/loading.tsx`

### 工具文件
- 使用kebab-case或camelCase：`utils.ts`, `auth-tools.ts`
- 类型定义使用 `types.ts`：`resume-types.ts`
- 配置文件使用 `config.ts`：`colors.ts`

### 路由约定
- API路由使用 `route.ts`：`api/chat/route.ts`
- 动态路由使用方括号：`[id]/`, `[user-id]/`
- 路由组使用圆括号：`(dashboard)/`

## 组件组织原则

### 按功能分组
- 每个主要功能有独立的文件夹
- 相关组件放在同一目录下
- 共享组件放在 `shared/` 子目录

### 层次结构
- 页面组件 → 容器组件 → 展示组件
- 布局组件独立组织
- UI基础组件统一管理

### 复用性
- 通用组件放在 `components/ui/`
- 业务特定组件放在对应功能目录
- 可复用组件放在 `components/shared/`

## 数据流架构

### 状态管理
- **全局状态**: React Context
- **本地状态**: useState, useReducer
- **服务端状态**: Server Actions
- **缓存**: Redis, Supabase

### 数据获取
- **服务端**: Server Actions, Supabase直接查询
- **客户端**: React Query, SWR模式
- **实时数据**: Supabase Realtime

### 类型安全
- 使用Zod进行运行时验证
- TypeScript类型定义在 `src/lib/types.ts`
- 组件props使用interface定义

## 关键模块说明

### 简历编辑器
- 位置：`src/components/resume/editor/`
- 包含：编辑面板、预览面板、AI助手、可调整面板
- 支持实时预览、拖拽编辑、富文本编辑
- 集成AI生成和优化功能

### AI集成
- 工具函数：`src/lib/tools.ts`
- 提示词模板：`src/lib/prompts.ts`
- 支持多个AI提供商（OpenAI、Anthropic、Google、DeepSeek、Groq、OpenRouter）
- 简历定制化、岗位匹配、STAR框架优化
- 速率限制和用户等级管理

### 职位管理
- 组件：`src/components/jobs/`
- API：`src/utils/actions/jobs/`
- 功能：职位创建、编辑、删除、筛选
- 支持远程/现场/混合、全职/兼职/实习分类
- 管理员权限管理

### 认证系统
- 组件：`src/components/auth/`
- 工具：`src/utils/auth.ts`、`src/utils/auth-cache.ts`
- 配置：`src/utils/supabase/`
- 支持邮箱/密码登录、密码重置

### 设计令牌系统
- 核心文件：`src/lib/design-tokens.ts`
- 配置文件：`src/config/colors.ts`
- 全局样式：`src/app/globals.css`
- 功能：统一管理颜色、间距、圆角、阴影等设计变量
- 特色：85%黑白+5%灰色+10%品牌蓝的极简设计理念
- 工具：提供类型安全的设计令牌访问和验证功能
- 文档：详细的`DESIGN_TOKENS_USAGE.md`使用指南

### 支付系统
- 组件：`src/components/subscription/`、`src/components/pricing/`
- API：`src/utils/actions/stripe/`、`src/utils/actions/subscriptions/`
- Webhook：`src/app/api/webhooks/stripe/`
- 支持免费/Pro订阅模式

### 求职信功能
- 组件：`src/components/cover-letter/`
- API：`src/utils/actions/cover-letter/`
- 支持AI生成和编辑

### 性能优化
- 工具：`src/lib/performance/`
- 缓存：`src/lib/cache/`、`src/lib/redis.ts`
- 速率限制：`src/lib/rateLimiter.ts`