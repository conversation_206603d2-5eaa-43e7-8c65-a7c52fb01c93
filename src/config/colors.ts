/**
 * 统一颜色配置文件 - 极简黑白主题
 * 品牌：SnapOffer
 * 设计理念：85% 黑白 + 5% 灰色 + 10% 品牌蓝点缀
 * 使用新的设计令牌系统
 */

import { designTokens } from '../lib/design-tokens';

export const THEME = {
  // 基础颜色
  WHITE: designTokens.colors.white,
  BLACK: designTokens.colors.black,
  
  // 灰色系（5% 使用比例）
  GRAY: designTokens.colors.gray,
  
  // 品牌蓝（10% 使用比例）
  BLUE: designTokens.colors.blue,
  
  // 功能色（极少使用）
  RED: designTokens.colors.red,
  GREEN: designTokens.colors.green,
  YELLOW: designTokens.colors.yellow,
  
  // 透明度
  OPACITY: designTokens.colors.opacity,
} as const;

// Tailwind 颜色映射
export const TAILWIND_COLORS = {
  // 基础色
  white: THEME.WHITE,
  black: THEME.BLACK,
  
  // 灰色系
  gray: {
    50: THEME.GRAY[50],
    100: THEME.GRAY[100],
    200: THEME.GRAY[200],
    300: THEME.GRAY[300],
    500: THEME.GRAY[500],
    700: THEME.GRAY[700],
    900: THEME.GRAY[900],
  },
  
  // 品牌蓝
  blue: {
    400: THEME.BLUE[400],
    500: THEME.BLUE[500],
    600: THEME.BLUE[600],
    700: THEME.BLUE[700],
  },
  
  // 功能色
  red: {
    500: THEME.RED[500],
    600: THEME.RED[600],
  },
} as const;

// CSS 变量映射 - 专注于浅色模式
export const CSS_VARIABLES = {
  light: {
    background: '0 0% 100%',
    foreground: '0 0% 0%',
    card: '0 0% 100%',
    'card-foreground': '0 0% 0%',
    popover: '0 0% 100%',
    'popover-foreground': '0 0% 0%',
    primary: '217 91% 60%',
    'primary-foreground': '0 0% 100%',
    secondary: '0 0% 96%',
    'secondary-foreground': '0 0% 0%',
    muted: '0 0% 96%',
    'muted-foreground': '0 0% 45%',
    accent: '217 91% 60%',
    'accent-foreground': '0 0% 100%',
    destructive: '0 84% 60%',
    'destructive-foreground': '0 0% 100%',
    border: '0 0% 96%',
    input: '0 0% 100%',
    ring: '217 91% 60%',
    radius: '0.25rem',
  },
} as const;

// 颜色使用规则 - 使用新的设计令牌系统
export const COLOR_RULES = {
  // 使用比例
  USAGE_RATIO: {
    BLACK_WHITE: 85,  // 黑白占比
    GRAY: 5,          // 灰色占比
    BLUE: 10,         // 品牌蓝占比
    RED: 0,           // 红色占比（极少）
  },
  
  // 品牌蓝使用场景
  BLUE_USAGE_SCENARIOS: [
    'primary-cta-button',
    'links',
    'selected-state',
    'focus-ring',
    'loading-indicator',
    'active-state',
  ],
  
  // 禁止使用品牌蓝的场景
  BLUE_RESTRICTED_SCENARIOS: [
    'backgrounds',
    'card-backgrounds',
    'secondary-buttons',
    'decorative-elements',
    'text-color-except-links',
  ],
  
  // 对比度要求
  CONTRAST_RATIO: {
    BODY_TEXT: 21,     // 正文文字对比度
    SECONDARY_TEXT: 4.5, // 次要文字对比度
    LINKS: 4.5,         // 链接对比度
    BUTTONS: 4.5,       // 按钮文字对比度
  },
} as const;

// 颜色使用场景类型
export type ColorScenario = 
  | 'primary-cta-button'
  | 'secondary-button'
  | 'link'
  | 'selected-state'
  | 'focus-ring'
  | 'loading-indicator'
  | 'active-state'
  | 'background'
  | 'card-background'
  | 'text-primary'
  | 'text-secondary'
  | 'border'
  | 'destructive-action'
  | 'success-state'
  | 'warning-state'
  | 'disabled-state';

// 颜色验证结果
export interface ColorValidationResult {
  isValid: boolean;
  message: string;
  suggestions?: string[];
}

// 颜色对比度信息
export interface ContrastInfo {
  ratio: number;
  level: 'AAA' | 'AA' | 'fail';
  requiresLargeText: boolean;
}

// 工具函数 - 使用新的设计令牌系统
export const ColorUtils = {
  // 获取对比度 - 完整实现
  getContrast: (color1: string, color2: string): number => {
    // 将十六进制颜色转换为 RGB
    const hexToRgb = (hex: string): [number, number, number] => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? [
        parseInt(result[1], 16),
        parseInt(result[2], 16),
        parseInt(result[3], 16)
      ] : [0, 0, 0];
    };

    // 计算相对亮度
    const getLuminance = (r: number, g: number, b: number): number => {
      const [rs, gs, bs] = [r, g, b].map(c => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    };

    const rgb1 = hexToRgb(color1);
    const rgb2 = hexToRgb(color2);
    
    const lum1 = getLuminance(rgb1[0], rgb1[1], rgb1[2]);
    const lum2 = getLuminance(rgb2[0], rgb2[1], rgb2[2]);
    
    const lighter = Math.max(lum1, lum2);
    const darker = Math.min(lum1, lum2);
    
    return (lighter + 0.05) / (darker + 0.05);
  },

  // 获取对比度详细信息
  getContrastInfo: (color1: string, color2: string): ContrastInfo => {
    const ratio = ColorUtils.getContrast(color1, color2);
    
    let level: 'AAA' | 'AA' | 'fail' = 'fail';
    let requiresLargeText = false;

    if (ratio >= 7) {
      level = 'AAA';
    } else if (ratio >= 4.5) {
      level = 'AA';
      requiresLargeText = true;
    }

    return { ratio, level, requiresLargeText };
  },

  // 检查颜色使用是否符合规范 - 完整实现
  validateColorUsage: (scenario: ColorScenario, colorPath: string): ColorValidationResult => {
    const colorPathLower = colorPath.toLowerCase();
    
    // 检查品牌蓝使用规范
    if (colorPathLower.includes('blue')) {
      const allowedBlueScenarios: ColorScenario[] = [
        'primary-cta-button',
        'link',
        'selected-state',
        'focus-ring',
        'loading-indicator',
        'active-state'
      ];
      
      if (!allowedBlueScenarios.includes(scenario)) {
        return {
          isValid: false,
          message: `品牌蓝不允许在 '${scenario}' 场景中使用`,
          suggestions: [
            '使用灰色系作为次要颜色',
            '使用黑白作为主要颜色',
            '考虑使用其他颜色系列'
          ]
        };
      }
    }

    // 检查功能色使用规范
    if (colorPathLower.includes('red') && scenario !== 'destructive-action') {
      return {
        isValid: false,
        message: `红色只允许在 'destructive-action' 场景中使用`,
        suggestions: [
          '使用灰色表示禁用状态',
          '使用品牌蓝表示主要操作',
          '使用绿色表示成功状态'
        ]
      };
    }

    if (colorPathLower.includes('green') && scenario !== 'success-state') {
      return {
        isValid: false,
        message: `绿色只允许在 'success-state' 场景中使用`,
        suggestions: [
          '使用灰色表示中性状态',
          '使用品牌蓝表示主要操作'
        ]
      };
    }

    if (colorPathLower.includes('yellow') && scenario !== 'warning-state') {
      return {
        isValid: false,
        message: `黄色只允许在 'warning-state' 场景中使用`,
        suggestions: [
          '使用灰色表示中性状态',
          '使用品牌蓝表示主要操作'
        ]
      };
    }

    return {
      isValid: true,
      message: `颜色使用符合规范`
    };
  },

  // 生成透明度变体 - 改进实现
  withOpacity: (color: string, opacity: number): string => {
    if (opacity < 0 || opacity > 1) {
      throw new Error('透明度必须在 0 到 1 之间');
    }
    
    // 移除 # 号
    const hex = color.replace('#', '');
    
    // 如果是 3 位 hex，转换为 6 位
    const fullHex = hex.length === 3 ? 
      hex.split('').map(c => c + c).join('') : 
      hex;
    
    // 添加透明度
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    
    return `#${fullHex}${alpha}`;
  },

  // 生成颜色的渐变
  generateGradient: (color1: string, color2: string, direction: 'to-right' | 'to-bottom' | 'to-top' | 'to-left' = 'to-right'): string => {
    return `linear-gradient(${direction}, ${color1}, ${color2})`;
  },

  // 获取颜色的互补色
  getComplementaryColor: (color: string): string => {
    const hex = color.replace('#', '');
    const r = (255 - parseInt(hex.substr(0, 2), 16)).toString(16).padStart(2, '0');
    const g = (255 - parseInt(hex.substr(2, 2), 16)).toString(16).padStart(2, '0');
    const b = (255 - parseInt(hex.substr(4, 2), 16)).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`;
  },

  // 获取颜色的浅色变体
  getLighterVariant: (color: string, amount: number = 20): string => {
    const hex = color.replace('#', '');
    const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + amount).toString(16).padStart(2, '0');
    const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + amount).toString(16).padStart(2, '0');
    const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + amount).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`;
  },

  // 获取颜色的深色变体
  getDarkerVariant: (color: string, amount: number = 20): string => {
    const hex = color.replace('#', '');
    const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - amount).toString(16).padStart(2, '0');
    const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - amount).toString(16).padStart(2, '0');
    const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - amount).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`;
  },

  // 检查颜色是否为深色
  isDarkColor: (color: string): boolean => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // 计算亮度
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness < 128;
  },

  // 获取推荐文本颜色
  getTextColor: (backgroundColor: string): string => {
    return ColorUtils.isDarkColor(backgroundColor) ? '#ffffff' : '#000000';
  },

  // 生成颜色调色板
  generatePalette: (baseColor: string): Record<string, string> => {
    return {
      50: ColorUtils.getLighterVariant(baseColor, 40),
      100: ColorUtils.getLighterVariant(baseColor, 30),
      200: ColorUtils.getLighterVariant(baseColor, 20),
      300: ColorUtils.getLighterVariant(baseColor, 10),
      400: ColorUtils.getLighterVariant(baseColor, 5),
      500: baseColor,
      600: ColorUtils.getDarkerVariant(baseColor, 10),
      700: ColorUtils.getDarkerVariant(baseColor, 20),
      800: ColorUtils.getDarkerVariant(baseColor, 30),
      900: ColorUtils.getDarkerVariant(baseColor, 40),
    };
  },

  // 格式化颜色值
  formatColor: (color: string, format: 'hex' | 'rgb' | 'hsl'): string => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    switch (format) {
      case 'rgb':
        return `rgb(${r}, ${g}, ${b})`;
      case 'hsl':
        const hsl = ColorUtils.rgbToHsl(r, g, b);
        return `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;
      default:
        return color;
    }
  },

  // RGB 转 HSL
  rgbToHsl: (r: number, g: number, b: number): { h: number; s: number; l: number } => {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      
      h /= 6;
    }

    return { h: Math.round(h * 360), s: Math.round(s * 100), l: Math.round(l * 100) };
  },

  // 检查颜色是否有效
  isValidColor: (color: string): boolean => {
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexRegex.test(color);
  },

  // 获取颜色使用建议
  getColorUsageSuggestions: (scenario: ColorScenario): string[] => {
    const suggestions: Record<ColorScenario, string[]> = {
      'primary-cta-button': [
        '使用品牌蓝作为主要颜色',
        '确保对比度达到 4.5:1',
        '添加悬停效果'
      ],
      'secondary-button': [
        '使用灰色系作为次要颜色',
        '保持视觉层次',
        '避免与主要按钮竞争'
      ],
      'link': [
        '使用品牌蓝',
        '添加下划线或悬停效果',
        '确保可访问性'
      ],
      'selected-state': [
        '使用品牌蓝表示选中',
        '添加适当的背景色',
        '保持一致性'
      ],
      'focus-ring': [
        '使用品牌蓝',
        '确保足够的对比度',
        '添加适当的轮廓'
      ],
      'loading-indicator': [
        '使用品牌蓝',
        '添加动画效果',
        '保持视觉一致性'
      ],
      'active-state': [
        '使用深色品牌蓝',
        '提供清晰的反馈',
        '保持响应性'
      ],
      'background': [
        '使用白色或浅灰色',
        '确保文字可读性',
        '避免过于鲜艳'
      ],
      'card-background': [
        '使用白色',
        '添加微妙的阴影',
        '保持简洁'
      ],
      'text-primary': [
        '使用黑色或深灰色',
        '确保对比度',
        '保持可读性'
      ],
      'text-secondary': [
        '使用灰色',
        '保持层次',
        '避免过于突出'
      ],
      'border': [
        '使用浅灰色',
        '保持微妙',
        '避免过于强烈'
      ],
      'destructive-action': [
        '使用红色',
        '提供清晰的警告',
        '谨慎使用'
      ],
      'success-state': [
        '使用绿色',
        '提供积极的反馈',
        '保持一致性'
      ],
      'warning-state': [
        '使用黄色',
        '提供适当的警告',
        '避免过于刺眼'
      ],
      'disabled-state': [
        '使用浅灰色',
        '降低对比度',
        '明确表示不可用'
      ]
    };

    return suggestions[scenario] || [];
  }
} as const;

export default THEME;