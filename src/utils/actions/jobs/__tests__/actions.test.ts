/**
 * 岗位申请管理 MVP - Server Actions 单元测试
 * 
 * 测试覆盖：
 * - setSystemStage: 系统阶段变更功能
 * - addCustomStage/removeCustomStage: 自定义阶段管理
 * - addNote/softDeleteNote: 笔记管理
 * - updateEventTime: 事件时间修改
 * - updateJobFields: 岗位字段更新
 */

import { 
  setSystemStage,
  addCustomStage,
  removeCustomStage,
  addNote,
  softDeleteNote,
  updateEventTime,
  updateJobFields,
  SystemStage,
  ApplicationLogEvent,
  EventMatcher
} from '../actions';
import { createClient } from '@/utils/supabase/server';

// Mock Supabase client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn()
}));

// Mock revalidatePath
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn()
}));

const mockSupabase = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn()
        }))
      }))
    })),
    update: jest.fn(() => ({
      eq: jest.fn(() => ({
        eq: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn()
          }))
        }))
      }))
    }))
  }))
};

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>'
};

const mockJob = {
  id: 'job-123',
  user_id: 'user-123',
  position_title: '前端开发工程师',
  company_name: '测试公司',
  application_status: '申请' as SystemStage,
  application_log: [] as ApplicationLogEvent[]
};

describe('岗位申请管理 Server Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockResolvedValue(mockSupabase);
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: mockUser },
      error: null
    });
  });

  describe('setSystemStage - 系统阶段变更', () => {
    it('应该成功更新系统阶段并追加事件日志', async () => {
      const initialLog: ApplicationLogEvent[] = [];
      
      // Mock 数据库查询
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: { ...mockJob, application_status: '面试' },
                  error: null
                })
              })
            })
          })
        })
      });

      const result = await setSystemStage('job-123', '申请', '面试');

      expect(result.success).toBe(true);
      expect(result.job.application_status).toBe('面试');
      
      // 验证更新调用
      expect(mockSupabase.from).toHaveBeenCalledWith('jobs');
    });

    it('应该在用户未认证时返回错误', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Not authenticated' }
      });

      const result = await setSystemStage('job-123', '申请', '面试');

      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated');
    });

    it('应该在数据库查询失败时返回错误', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { message: 'Job not found' }
              })
            })
          })
        })
      });

      const result = await setSystemStage('job-123', '申请', '面试');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch job data');
    });
  });

  describe('addCustomStage - 添加自定义阶段', () => {
    it('应该成功添加自定义阶段', async () => {
      const initialLog: ApplicationLogEvent[] = [];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockJob,
                  error: null
                })
              })
            })
          })
        })
      });

      const result = await addCustomStage('job-123', '技术评估');

      expect(result.success).toBe(true);
    });

    it('应该在阶段名称为空时返回错误', async () => {
      const result = await addCustomStage('job-123', '');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Stage name cannot be empty');
    });

    it('应该在阶段名称只有空格时返回错误', async () => {
      const result = await addCustomStage('job-123', '   ');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Stage name cannot be empty');
    });
  });

  describe('removeCustomStage - 移除自定义阶段', () => {
    it('应该成功移除自定义阶段', async () => {
      const initialLog: ApplicationLogEvent[] = [
        {
          type: 'custom_stage_add',
          name: '技术评估',
          created_at: '2024-01-15T10:00:00Z'
        }
      ];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockJob,
                  error: null
                })
              })
            })
          })
        })
      });

      const result = await removeCustomStage('job-123', '技术评估');

      expect(result.success).toBe(true);
    });
  });

  describe('addNote - 添加笔记', () => {
    it('应该成功添加笔记', async () => {
      const initialLog: ApplicationLogEvent[] = [];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockJob,
                  error: null
                })
              })
            })
          })
        })
      });

      const result = await addNote('job-123', 'note-1', '这是一条测试笔记');

      expect(result.success).toBe(true);
    });

    it('应该在笔记ID为空时返回错误', async () => {
      const result = await addNote('job-123', '', '测试笔记');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Note ID cannot be empty');
    });

    it('应该在笔记内容为空时返回错误', async () => {
      const result = await addNote('job-123', 'note-1', '');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Note content cannot be empty');
    });
  });

  describe('softDeleteNote - 软删除笔记', () => {
    it('应该成功软删除已存在的笔记', async () => {
      const initialLog: ApplicationLogEvent[] = [
        {
          type: 'note_add',
          id: 'note-1',
          note: '要删除的笔记',
          created_at: '2024-01-15T10:00:00Z'
        }
      ];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockJob,
                  error: null
                })
              })
            })
          })
        })
      });

      const result = await softDeleteNote('job-123', 'note-1');

      expect(result.success).toBe(true);
    });

    it('应该在笔记不存在时返回错误', async () => {
      const initialLog: ApplicationLogEvent[] = [];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        })
      });

      const result = await softDeleteNote('job-123', 'non-existent-note');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Note not found');
    });
  });

  describe('updateEventTime - 更新事件时间', () => {
    it('应该成功更新笔记事件的时间', async () => {
      const initialLog: ApplicationLogEvent[] = [
        {
          type: 'note_add',
          id: 'note-1',
          note: '测试笔记',
          created_at: '2024-01-15T10:00:00Z'
        }
      ];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: mockJob,
                  error: null
                })
              })
            })
          })
        })
      });

      const matcher: EventMatcher = {
        type: 'note_add',
        id: 'note-1'
      };

      const result = await updateEventTime('job-123', matcher, '2024-01-16T10:00:00Z');

      expect(result.success).toBe(true);
    });

    it('应该在找不到匹配事件时返回错误', async () => {
      const initialLog: ApplicationLogEvent[] = [];
      
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: { application_log: initialLog },
                error: null
              })
            })
          })
        })
      });

      const matcher: EventMatcher = {
        type: 'note_add',
        id: 'non-existent-note'
      };

      const result = await updateEventTime('job-123', matcher, '2024-01-16T10:00:00Z');

      expect(result.success).toBe(false);
      expect(result.error).toBe('No matching event found');
    });

    it('应该在时间格式无效时返回错误', async () => {
      const matcher: EventMatcher = {
        type: 'note_add',
        id: 'note-1'
      };

      const result = await updateEventTime('job-123', matcher, 'invalid-date');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid datetime format');
    });
  });

  describe('updateJobFields - 更新岗位字段', () => {
    it('应该成功更新岗位字段', async () => {
      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              select: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: { ...mockJob, position_title: '高级前端开发工程师' },
                  error: null
                })
              })
            })
          })
        })
      });

      const result = await updateJobFields('job-123', {
        position_title: '高级前端开发工程师',
        salary_range: '20K-30K'
      });

      expect(result.success).toBe(true);
      expect(result.job.position_title).toBe('高级前端开发工程师');
    });

    it('应该在没有字段更新时返回错误', async () => {
      const result = await updateJobFields('job-123', {});

      expect(result.success).toBe(false);
      expect(result.error).toBe('No fields to update');
    });

    it('应该在所有字段都是空值时返回错误', async () => {
      const result = await updateJobFields('job-123', {
        position_title: undefined,
        company_name: null
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('No valid fields to update');
    });
  });

  describe('边界条件和错误处理', () => {
    it('所有函数都应该在意外错误时返回通用错误消息', async () => {
      mockSupabase.auth.getUser.mockRejectedValue(new Error('Network error'));

      const results = await Promise.all([
        setSystemStage('job-123', '申请', '面试'),
        addCustomStage('job-123', '测试阶段'),
        removeCustomStage('job-123', '测试阶段'),
        addNote('job-123', 'note-1', '测试笔记'),
        softDeleteNote('job-123', 'note-1'),
        updateJobFields('job-123', { position_title: '新职位' })
      ]);

      results.forEach(result => {
        expect(result.success).toBe(false);
        expect(result.error).toBe('An unexpected error occurred');
      });
    });
  });
});
