'use server';

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from 'next/cache';
import { simplifiedJobSchema } from "@/lib/zod-schemas";
import type { Job, SystemStage, ApplicationLogEvent, EventMatcher } from "@/lib/types";
import { z } from "zod";
import { JobListingParams } from "./schema";

export async function createJob(jobListing: z.infer<typeof simplifiedJobSchema>) {
  
  const supabase = await createClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user) {
    throw new Error('User not authenticated');
  }

  const jobData = {
    user_id: user.id,
    company_name: jobListing.company_name,
    position_title: jobListing.position_title,
    job_url: jobListing.job_url,
    description: jobListing.description,
    location: jobListing.location,
    salary_range: jobListing.salary_range,
    keywords: jobListing.keywords,
    work_location: jobListing.work_location || 'in_person',
    employment_type: jobListing.employment_type || 'full_time',
    is_active: true
  };

  const { data, error } = await supabase
    .from('jobs')
    .insert([jobData])
    .select()
    .single();

  if (error) {
    console.error('[createJob] Error creating job:', error);
    throw error;
  }
  
  return data;
}

export async function deleteJob(jobId: string): Promise<void> {
  const supabase = await createClient();
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    throw new Error('User not authenticated');
  }

  // First, get all resumes that reference this job
  const { data: affectedResumes } = await supabase
    .from('resumes')
    .select('id')
    .eq('job_id', jobId);

  // Delete the job
  const { error: deleteError } = await supabase
    .from('jobs')
    .delete()
    .eq('id', jobId);

  if (deleteError) {
    console.error('Delete error:', deleteError);
    throw new Error('Failed to delete job');
  }

  // Revalidate all affected resume paths
  affectedResumes?.forEach(resume => {
    revalidatePath(`/resumes/${resume.id}`);
  });

  // Also revalidate the general paths
  revalidatePath('/', 'layout');
  revalidatePath('/resumes', 'layout');
}


export async function getJobListings({ 
  page = 1, 
  pageSize = 10, 
  filters 
}: JobListingParams) {
  const supabase = await createClient();

  // Calculate offset
  const offset = (page - 1) * pageSize;

  // Start building the query
  let query = supabase
    .from('jobs')
    .select('*', { count: 'exact' })
    .eq('is_active', true)
    .order('created_at', { ascending: false });

  // Apply filters if they exist
  if (filters) {
    if (filters.workLocation) {
      query = query.eq('work_location', filters.workLocation);
    }
    if (filters.employmentType) {
      query = query.eq('employment_type', filters.employmentType);
    }
    if (filters.keywords && filters.keywords.length > 0) {
      query = query.contains('keywords', filters.keywords);
    }
  }

  // Add pagination
  const { data: jobs, error, count } = await query
    .range(offset, offset + pageSize - 1);

  if (error) {
    console.error('Error fetching jobs:', error);
    throw new Error('Failed to fetch job listings');
  }

  return {
    jobs,
    totalCount: count ?? 0,
    currentPage: page,
    totalPages: Math.ceil((count ?? 0) / pageSize)
  };
}

export async function deleteTailoredJob(jobId: string): Promise<void> {
  const supabase = await createClient();

  const { error } = await supabase
    .from('jobs')
    .update({ is_active: false })
    .eq('id', jobId);

  if (error) {
    throw new Error('Failed to delete job');
  }

  revalidatePath('/', 'layout');
}

export async function createEmptyJob(): Promise<Job> {
  const supabase = await createClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user) {
    throw new Error('User not authenticated');
  }

  const emptyJob: Partial<Job> = {
    user_id: user.id,
    company_name: 'New Company',
    position_title: 'New Position',
    job_url: null,
    description: null,
    location: null,
    salary_range: null,
    keywords: [],
    work_location: null,
    employment_type: null,
    is_active: true
  };

  const { data, error } = await supabase
    .from('jobs')
    .insert([emptyJob])
    .select()
    .single();

  if (error) {
    console.error('Error creating job:', error);
    throw new Error('Failed to create job');
  }

  revalidatePath('/', 'layout');
  return data;
}

/**
 * 获取与特定职位关联的简历列表
 * @param jobId 职位ID
 * @returns 关联的简历数组
 */
export async function getResumesByJobId(jobId: string) {
  const supabase = await createClient();
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user) {
    throw new Error('User not authenticated');
  }

  const { data: resumes, error } = await supabase
    .from('resumes')
    .select('*')
    .eq('job_id', jobId)
    .eq('user_id', user.id)
    .eq('is_base_resume', false) // 只获取非基础简历（定制简历）
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching resumes for job:', error);
    throw new Error('Failed to fetch resumes');
  }

  return resumes || [];
}

// ===========================
// 岗位申请管理 MVP - Server Actions
// ===========================

/**
 * 设置系统阶段 - 更新 application_status 并追加 status_changed 事件
 * @param jobId 岗位ID
 * @param from 当前阶段 (可为null，表示首次设置)
 * @param to 目标阶段
 * @param created_at 可选的事件时间，默认为当前时间
 * @returns 更新后的岗位数据
 */
export async function setSystemStage(
  jobId: string,
  from: SystemStage | null,
  to: SystemStage,
  created_at?: string
): Promise<{ success: boolean; error?: string; job?: Job }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // 创建状态变更事件
    const statusEvent: ApplicationLogEvent = {
      type: 'status_changed',
      from,
      to,
      created_at: created_at || new Date().toISOString()
    };

    // 先获取当前岗位数据以获取现有日志
    const { data: currentJob, error: fetchError } = await supabase
      .from('jobs')
      .select('application_log')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('[setSystemStage] Error fetching job:', fetchError);
      return { success: false, error: 'Failed to fetch job data' };
    }

    const currentLog = (currentJob.application_log as ApplicationLogEvent[]) || [];
    const updatedLog = [...currentLog, statusEvent];

    // 检查是否是系统阶段
    const systemStages: SystemStage[] = ['申请', '笔试', '面试', '谈判', '录用', '拒绝'];
    const isSystemStage = systemStages.includes(to);
    
    // 只有系统阶段才更新application_status字段，自定义阶段不更新该字段
    const updateData: any = {
      application_log: updatedLog
    };
    
    if (isSystemStage) {
      updateData.application_status = to;
    }

    // 更新岗位状态和日志
    const { data: updatedJob, error: updateError } = await supabase
      .from('jobs')
      .update(updateData)
      .eq('id', jobId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('[setSystemStage] Error updating job:', updateError);
      return { success: false, error: 'Failed to update job status' };
    }

    // 重新验证相关页面缓存
    revalidatePath('/jobs');
    revalidatePath(`/jobs/${jobId}`);

    return { success: true, job: updatedJob };
  } catch (error) {
    console.error('[setSystemStage] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * 添加自定义阶段 - 在 application_log 中追加 custom_stage_add 事件
 * @param jobId 岗位ID
 * @param name 自定义阶段名称
 * @param created_at 可选的事件时间，默认为当前时间
 * @returns 操作结果
 */
export async function addCustomStage(
  jobId: string,
  name: string,
  created_at?: string
): Promise<{ success: boolean; error?: string; job?: Job }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // 验证阶段名称不为空
    if (!name || name.trim().length === 0) {
      return { success: false, error: 'Stage name cannot be empty' };
    }

    const stageName = name.trim();

    // 创建自定义阶段添加事件
    const customStageEvent: ApplicationLogEvent = {
      type: 'custom_stage_add',
      name: stageName,
      created_at: created_at || new Date().toISOString()
    };

    // 先获取当前岗位数据以获取现有日志
    const { data: currentJob, error: fetchError } = await supabase
      .from('jobs')
      .select('application_log')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('[addCustomStage] Error fetching job:', fetchError);
      return { success: false, error: 'Failed to fetch job data' };
    }

    const currentLog = (currentJob.application_log as ApplicationLogEvent[]) || [];
    const updatedLog = [...currentLog, customStageEvent];

    // 更新岗位日志
    const { data: updatedJob, error: updateError } = await supabase
      .from('jobs')
      .update({ application_log: updatedLog })
      .eq('id', jobId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('[addCustomStage] Error updating job:', updateError);
      return { success: false, error: 'Failed to add custom stage' };
    }

    // 重新验证相关页面缓存
    revalidatePath('/jobs');
    revalidatePath(`/jobs/${jobId}`);

    return { success: true, job: updatedJob };
  } catch (error) {
    console.error('[addCustomStage] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * 移除自定义阶段 - 直接从日志中移除相关事件，不添加删除记录
 * @param jobId 岗位ID
 * @param name 自定义阶段名称
 * @returns 操作结果
 */
export async function removeCustomStage(
  jobId: string,
  name: string
): Promise<{ success: boolean; error?: string; job?: Job }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // 验证阶段名称不为空
    if (!name || name.trim().length === 0) {
      return { success: false, error: 'Stage name cannot be empty' };
    }

    const stageName = name.trim();

    // 先获取当前岗位数据以获取现有日志
    const { data: currentJob, error: fetchError } = await supabase
      .from('jobs')
      .select('application_log')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('[removeCustomStage] Error fetching job:', fetchError);
      return { success: false, error: 'Failed to fetch job data' };
    }

    const currentLog = (currentJob.application_log as ApplicationLogEvent[]) || [];
    
    // 移除所有与该自定义阶段相关的事件（添加和删除事件都移除）
    const updatedLog = currentLog.filter(event => {
      if ((event.type === 'custom_stage_add' || event.type === 'custom_stage_remove') && event.name === stageName) {
        return false; // 移除这个事件
      }
      return true; // 保留其他事件
    });

    // 更新岗位日志
    const { data: updatedJob, error: updateError } = await supabase
      .from('jobs')
      .update({ application_log: updatedLog })
      .eq('id', jobId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('[removeCustomStage] Error updating job:', updateError);
      return { success: false, error: 'Failed to remove custom stage' };
    }

    // 重新验证相关页面缓存
    revalidatePath('/jobs');
    revalidatePath(`/jobs/${jobId}`);

    return { success: true, job: updatedJob };
  } catch (error) {
    console.error('[removeCustomStage] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * 添加笔记 - 在 application_log 中追加 note_add 事件
 * @param jobId 岗位ID
 * @param id 笔记唯一标识符（由前端生成的UUID）
 * @param note 笔记内容
 * @param created_at 可选的事件时间，默认为当前时间
 * @returns 操作结果
 */
export async function addNote(
  jobId: string,
  id: string,
  note: string,
  created_at?: string
): Promise<{ success: boolean; error?: string; job?: Job }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // 验证必要的参数
    if (!id || id.trim().length === 0) {
      return { success: false, error: 'Note ID cannot be empty' };
    }

    if (!note || note.trim().length === 0) {
      return { success: false, error: 'Note content cannot be empty' };
    }

    const noteId = id.trim();
    const noteContent = note.trim();

    // 创建笔记添加事件
    const noteEvent: ApplicationLogEvent = {
      type: 'note_add',
      id: noteId,
      note: noteContent,
      created_at: created_at || new Date().toISOString()
    };

    // 先获取当前岗位数据以获取现有日志
    const { data: currentJob, error: fetchError } = await supabase
      .from('jobs')
      .select('application_log')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('[addNote] Error fetching job:', fetchError);
      return { success: false, error: 'Failed to fetch job data' };
    }

    const currentLog = (currentJob.application_log as ApplicationLogEvent[]) || [];
    const updatedLog = [...currentLog, noteEvent];

    // 更新岗位日志
    const { data: updatedJob, error: updateError } = await supabase
      .from('jobs')
      .update({ application_log: updatedLog })
      .eq('id', jobId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('[addNote] Error updating job:', updateError);
      return { success: false, error: 'Failed to add note' };
    }

    // 重新验证相关页面缓存
    revalidatePath('/jobs');
    revalidatePath(`/jobs/${jobId}`);

    return { success: true, job: updatedJob };
  } catch (error) {
    console.error('[addNote] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * 软删除笔记 - 在 application_log 中追加 note_remove 事件
 * @param jobId 岗位ID
 * @param id 笔记唯一标识符
 * @param created_at 可选的事件时间，默认为当前时间
 * @returns 操作结果
 */
export async function softDeleteNote(
  jobId: string,
  id: string,
  created_at?: string
): Promise<{ success: boolean; error?: string; job?: Job }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // 验证笔记ID不为空
    if (!id || id.trim().length === 0) {
      return { success: false, error: 'Note ID cannot be empty' };
    }

    const noteId = id.trim();

    // 创建笔记删除事件
    const noteEvent: ApplicationLogEvent = {
      type: 'note_remove',
      id: noteId,
      created_at: created_at || new Date().toISOString()
    };

    // 先获取当前岗位数据以获取现有日志
    const { data: currentJob, error: fetchError } = await supabase
      .from('jobs')
      .select('application_log')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('[softDeleteNote] Error fetching job:', fetchError);
      return { success: false, error: 'Failed to fetch job data' };
    }

    const currentLog = (currentJob.application_log as ApplicationLogEvent[]) || [];
    
    // 验证要删除的笔记是否存在
    const noteExists = currentLog.some(event => 
      event.type === 'note_add' && event.id === noteId
    );
    
    if (!noteExists) {
      return { success: false, error: 'Note not found' };
    }

    const updatedLog = [...currentLog, noteEvent];

    // 更新岗位日志
    const { data: updatedJob, error: updateError } = await supabase
      .from('jobs')
      .update({ application_log: updatedLog })
      .eq('id', jobId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('[softDeleteNote] Error updating job:', updateError);
      return { success: false, error: 'Failed to delete note' };
    }

    // 重新验证相关页面缓存
    revalidatePath('/jobs');
    revalidatePath(`/jobs/${jobId}`);

    return { success: true, job: updatedJob };
  } catch (error) {
    console.error('[softDeleteNote] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}



/**
 * 更新事件时间 - 在 application_log 中定位并仅更新匹配事件的 created_at
 * @param jobId 岗位ID
 * @param matcher 事件匹配器，用于定位要修改的事件
 * @param created_at 新的事件时间
 * @returns 操作结果
 */
export async function updateEventTime(
  jobId: string,
  matcher: EventMatcher,
  created_at: string
): Promise<{ success: boolean; error?: string; job?: Job }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // 验证必要的参数
    if (!matcher.type) {
      return { success: false, error: 'Event type is required' };
    }

    if (!created_at) {
      return { success: false, error: 'New event time is required' };
    }

    // 验证新时间格式是否为有效的ISO8601
    try {
      new Date(created_at);
    } catch {
      return { success: false, error: 'Invalid datetime format' };
    }

    // 先获取当前岗位数据以获取现有日志
    const { data: currentJob, error: fetchError } = await supabase
      .from('jobs')
      .select('application_log')
      .eq('id', jobId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('[updateEventTime] Error fetching job:', fetchError);
      return { success: false, error: 'Failed to fetch job data' };
    }

    const currentLog = (currentJob.application_log as ApplicationLogEvent[]) || [];
    
    // 查找第一个匹配的事件
    let matchedEventIndex = -1;
    
    for (let i = 0; i < currentLog.length; i++) {
      const event = currentLog[i];
      
      // 基本类型匹配
      if (event.type !== matcher.type) continue;
      
      // 根据事件类型进行详细匹配
      let isMatch = false;
      
      switch (matcher.type) {
        case 'note_add':
        case 'note_remove':
          // 笔记事件：匹配 id
          isMatch = matcher.id && event.id === matcher.id;
          break;
          
        case 'custom_stage_add':
        case 'custom_stage_remove':
          // 自定义阶段事件：匹配 name
          isMatch = matcher.name && event.name === matcher.name;
          // 如果提供了原始时间，也需要匹配
          if (isMatch && matcher.created_at) {
            isMatch = event.created_at === matcher.created_at;
          }
          break;
          
        case 'status_changed':
          // 状态变更事件：匹配 from 和 to
          isMatch = true;
          if (matcher.from !== undefined) {
            isMatch = isMatch && event.from === matcher.from;
          }
          if (matcher.to !== undefined) {
            isMatch = isMatch && event.to === matcher.to;
          }
          // 如果提供了原始时间，也需要匹配
          if (isMatch && matcher.created_at) {
            isMatch = event.created_at === matcher.created_at;
          }
          break;
      }
      
      if (isMatch) {
        matchedEventIndex = i;
        break;
      }
    }
    
    if (matchedEventIndex === -1) {
      return { success: false, error: 'No matching event found' };
    }

    // 创建更新后的日志，只修改匹配事件的时间
    const updatedLog = currentLog.map((event, index) => {
      if (index === matchedEventIndex) {
        return { ...event, created_at };
      }
      return event;
    });

    // 更新岗位日志
    const { data: updatedJob, error: updateError } = await supabase
      .from('jobs')
      .update({ application_log: updatedLog })
      .eq('id', jobId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('[updateEventTime] Error updating job:', updateError);
      return { success: false, error: 'Failed to update event time' };
    }

    // 重新验证相关页面缓存
    revalidatePath('/jobs');
    revalidatePath(`/jobs/${jobId}`);

    return { success: true, job: updatedJob };
  } catch (error) {
    console.error('[updateEventTime] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

/**
 * 更新岗位字段 - 仅更新提供的字段
 * @param jobId 岗位ID  
 * @param partialJob 要更新的字段（部分岗位数据）
 * @returns 操作结果
 */
export async function updateJobFields(
  jobId: string,
  partialJob: {
    position_title?: string;
    company_name?: string;
    location?: string;
    employment_type?: 'full_time' | 'part_time' | 'co_op' | 'internship' | 'contract';
    work_location?: 'remote' | 'in_person' | 'hybrid';
    job_url?: string;
    salary_range?: string;
    description?: string;
  }
): Promise<{ success: boolean; error?: string; job?: Job }> {
  try {
    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // 验证至少有一个字段被提供
    if (Object.keys(partialJob).length === 0) {
      return { success: false, error: 'No fields to update' };
    }

    // 过滤掉空值并准备更新数据
    const updateData: Record<string, string | null> = {};
    
    Object.entries(partialJob).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        updateData[key] = value;
      }
    });

    if (Object.keys(updateData).length === 0) {
      return { success: false, error: 'No valid fields to update' };
    }

    // 更新岗位字段
    const { data: updatedJob, error: updateError } = await supabase
      .from('jobs')
      .update(updateData)
      .eq('id', jobId)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('[updateJobFields] Error updating job:', updateError);
      return { success: false, error: 'Failed to update job fields' };
    }

    // 重新验证相关页面缓存
    revalidatePath('/jobs');
    revalidatePath(`/jobs/${jobId}`);

    return { success: true, job: updatedJob };
  } catch (error) {
    console.error('[updateJobFields] Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
} 