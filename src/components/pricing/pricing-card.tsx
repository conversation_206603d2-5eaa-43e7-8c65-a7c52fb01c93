'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Check, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Plan {
  title: string;
  priceId: string;
  price: string;
  features: string[];
}

interface PricingCardProps {
  plan: Plan;
  isCurrentPlan: boolean;
  isLoading: boolean;
  onAction: (plan: Plan) => Promise<void>;
  buttonText?: string;
  variant?: 'default' | 'pro' | 'canceling';
  className?: string;
}

export function PricingCard({ 
  plan, 
  isCurrentPlan, 
  isLoading,
  onAction,
  buttonText,
  variant = 'default',
  className
}: PricingCardProps) {
  const isFree = plan.title === 'Free';
  const isPro = plan.title === 'Pro';
  const isProVariant = (variant === 'pro' || variant === 'canceling') && isPro;
  
  const getSolidColors = () => {
    if (variant === 'canceling' && isPro) {
      return {
        card: "border-gray-200/50 bg-gray-50/30",
        text: "text-gray-600",
        button: "bg-gray-600 hover:bg-gray-700",
        check: "text-gray-500"
      };
    }
    if (variant === 'pro' && isPro) {
      return {
        card: "border-gray-200/50 bg-gray-50/30",
        text: "text-gray-600",
        button: "bg-gray-600 hover:bg-gray-700",
        check: "text-gray-500"
      };
    }
    return {
      card: "border-border/50 bg-background/50",
      text: "",
      button: "",
      check: "text-gray-500"
    };
  };

  const colors = getSolidColors();
  
  return (
    <Card className={cn(
      "relative p-8 rounded-2xl backdrop-blur-xl flex flex-col",
      colors.card,
      className
    )}>
      <div className="mb-8">
        <h3 className="text-2xl font-semibold mb-2">{plan.title}</h3>
        <div className="flex items-baseline gap-1">
          <span className={cn(
            "text-4xl font-bold",
            isProVariant && `bg-gray-100 ${colors.text} bg-clip-text text-transparent`
          )}>{plan.price}</span>
          <span className="text-muted-foreground">/month</span>
        </div>
      </div>

      <ul className="space-y-4 mb-8">
        {plan.features.map((feature) => (
          <li key={feature} className="flex items-center gap-2">
            <Check className={cn("h-5 w-5", colors.check)} />
            <span>{feature}</span>
          </li>
        ))}
      </ul>

      {!isFree && (
        <div className="mt-auto">
          <Button
            className={cn(
              "w-full",
              isProVariant && `bg-gray-600 ${colors.button} text-white`
            )}
            variant={isProVariant ? 'default' : 'outline'}
            onClick={() => onAction(plan)}
            disabled={isLoading || isCurrentPlan}
          >
            {isLoading && isCurrentPlan ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            {buttonText || (
              isCurrentPlan
                ? 'Cancel Subscription'
                : `Upgrade to ${plan.title}`
            )}
          </Button>
        </div>
      )}
    </Card>
  );
} 