'use client';

import { Resume } from "@/lib/types";
import { cn } from "@/lib/utils";
import { ResumePreview } from "../preview/resume-preview";
import CoverLetter from "@/components/cover-letter/cover-letter";
import { ResumeContextMenu } from "../preview/resume-context-menu";
import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area";
import * as React from "react";

// Custom white scrollbar for preview panel
const WhiteScrollBar = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>
>(({ className, orientation = "vertical", ...props }, ref) => (
  <ScrollAreaPrimitive.ScrollAreaScrollbar
    ref={ref}
    orientation={orientation}
    className={cn(
      "flex touch-none select-none transition-all duration-150 group",
      orientation === "vertical" &&
        "h-full w-2 border-l border-l-transparent p-[1px] hover:w-3",
      orientation === "horizontal" &&
        "h-2 flex-col border-t border-t-transparent p-[1px] hover:h-3",
      className
    )}
    {...props}
  >
    <ScrollAreaPrimitive.ScrollAreaThumb
      className={cn(
        "relative rounded-full transition-colors duration-150",
        "bg-white/60 hover:bg-white/80 group-hover:bg-white/90",
        "shadow-sm border border-gray-200/20",
        "flex-1"
      )}
    />
  </ScrollAreaPrimitive.ScrollAreaScrollbar>
))
WhiteScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName

const PreviewScrollArea = React.forwardRef<
  React.ElementRef<typeof ScrollAreaPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>
>(({ className, children, ...props }, ref) => (
  <ScrollAreaPrimitive.Root
    ref={ref}
    className={cn("relative overflow-hidden", className)}
    {...props}
  >
    <ScrollAreaPrimitive.Viewport className="h-full w-full rounded-[inherit]">
      {children}
    </ScrollAreaPrimitive.Viewport>
    <WhiteScrollBar />
    <ScrollAreaPrimitive.Corner />
  </ScrollAreaPrimitive.Root>
))
PreviewScrollArea.displayName = ScrollAreaPrimitive.Root.displayName

interface PreviewPanelProps {
  resume: Resume;
  onResumeChange: (field: keyof Resume, value: Resume[keyof Resume]) => void;
  width: number;
  // percentWidth: number;
}

export function PreviewPanel({
  resume,
  // onResumeChange,
  width
}: PreviewPanelProps) {
  return (
    <PreviewScrollArea className={cn(
      " z-50 h-full",
      resume.is_base_resume
        ? "bg-gray-50/30"
        : "bg-gray-50/60 shadow-sm shadow-gray-200/20"
    )}>
      <div className="w-full">
      <ResumeContextMenu resume={resume}>
          <ResumePreview resume={resume} containerWidth={width} />
        </ResumeContextMenu>
      </div>

      <div className="w-full">
        <CoverLetter
          // resumeId={resume.id}
          // hasCoverLetter={resume.has_cover_letter}
          // coverLetterData={resume.cover_letter}
          containerWidth={width}
          // onCoverLetterChange={(data: Record<string, unknown>) => {
          //   if ('has_cover_letter' in data) {
          //     onResumeChange('has_cover_letter', data.has_cover_letter as boolean);
          //   }
          //   if ('cover_letter' in data) {
          //     onResumeChange('cover_letter', data.cover_letter as Record<string, unknown>);
          //   }
          // }}
        />
      </div>
    </PreviewScrollArea>
  );
} 