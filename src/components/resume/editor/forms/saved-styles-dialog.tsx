import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DocumentSettings } from "@/lib/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState, useEffect } from "react";
import { Check, Save, Trash2, Plus } from "lucide-react";

interface SavedStylesDialogProps {
  currentSettings: DocumentSettings;
  onApplyStyle: (settings: DocumentSettings) => void;
}

interface SavedStyle {
  name: string;
  settings: DocumentSettings;
  timestamp: number;
}

export function SavedStylesDialog({ currentSettings, onApplyStyle }: SavedStylesDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [savedStyles, setSavedStyles] = useState<SavedStyle[]>([]);
  const [newStyleName, setNewStyleName] = useState("");
  const [isAddingNew, setIsAddingNew] = useState(false);

  // Load saved styles from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem("SnapOffer-saved-styles");
    if (saved) {
      setSavedStyles(JSON.parse(saved));
    }
  }, []);

  // Save current settings with name
  const handleSaveStyle = () => {
    if (!newStyleName.trim()) return;

    const newStyle: SavedStyle = {
      name: newStyleName,
      settings: currentSettings,
      timestamp: Date.now(),
    };

    const updatedStyles = [...savedStyles, newStyle];
    setSavedStyles(updatedStyles);
    localStorage.setItem("SnapOffer-saved-styles", JSON.stringify(updatedStyles));
    setNewStyleName("");
    setIsAddingNew(false);
  };

  // Delete a saved style
  const handleDeleteStyle = (timestamp: number) => {
    const updatedStyles = savedStyles.filter((style) => style.timestamp !== timestamp);
    setSavedStyles(updatedStyles);
    localStorage.setItem("SnapOffer-saved-styles", JSON.stringify(updatedStyles));
  };

  // Apply a saved style
  const handleApplyStyle = (settings: DocumentSettings) => {
    onApplyStyle(settings);
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="text-xs bg-white/80 hover:bg-gray-50/50
          border-gray-600 hover:border-gray-800 text-gray-700 hover:text-gray-800
          backdrop-blur-sm transition-all duration-500 hover:-translate-y-[1px] w-full
          shadow-sm hover:shadow-md"
        >
          <Save className="w-3 h-3 mr-1" />
          保存样式
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] bg-gray-100 from-white/95 to-white/90 
        backdrop-blur-2xl border-white/60 shadow-2xl pt-12">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl text-gray-700 font-semibold">
              保存的文档样式
            </DialogTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsAddingNew(true)}
              className="text-xs bg-white/80 hover:bg-gray-50/50
                border-gray-600/40 hover:border-gray-600 text-gray-700 hover:text-gray-800
                transition-all duration-500 hover:-translate-y-[1px]"
            >
              <Plus className="w-3 h-3 mr-1" />
              保存当前
            </Button>
          </div>
          <DialogDescription className="text-slate-600">
            保存当前文档设置或将保存的样式应用到您的简历。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {isAddingNew && (
            <div className="flex items-center gap-2 bg-gray-50/50
              p-4 rounded-xl border border-gray-200/30 shadow-sm">
              <Input
                placeholder="输入样式名称..."
                value={newStyleName}
                onChange={(e) => setNewStyleName(e.target.value)}
                className="flex-1 border-gray-200/40 focus:border-gray-400 bg-white/80"
                autoFocus
              />
              <Button
                onClick={handleSaveStyle}
                disabled={!newStyleName.trim()}
                size="sm"
                className="whitespace-nowrap bg-gray-600 hover:bg-gray-700
                  text-white transition-all
                  duration-500 hover:-translate-y-[1px] shadow-sm hover:shadow-md"
              >
                保存样式
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsAddingNew(false);
                  setNewStyleName("");
                }}
                className="text-slate-600 hover:text-slate-800"
              >
                取消
              </Button>
            </div>
          )}
          <div className={isAddingNew ? "" : "border-t border-teal-100 pt-4"}>
            <Label className="text-sm font-medium mb-2 block text-slate-700">保存的样式</Label>
            <ScrollArea className="h-[300px] rounded-xl border border-teal-200/30 bg-gray-100 
              from-white/50 to-white/30 backdrop-blur-sm">
              <div className="p-4 space-y-3">
                {savedStyles.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-slate-500">
                    <Save className="w-8 h-8 mb-2 opacity-50" />
                    <p className="text-sm">暂无保存的样式</p>
                  </div>
                ) : (
                  savedStyles.map((style) => (
                    <div
                      key={style.timestamp}
                      className="flex items-center justify-between group rounded-xl border 
                        border-teal-600 p-3 hover:bg-gray-100 hover:from-gray-50/50 
                        hover:to-gray-50/50 transition-all duration-500 hover:-translate-y-[1px] 
                        hover:shadow-sm"
                    >
                      <span className="text-sm font-medium text-slate-700">{style.name}</span>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleApplyStyle(style.settings)}
                          className="opacity-0 group-hover:opacity-100 transition-all duration-300 
                            text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                          title="应用样式"
                        >
                          <Check className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteStyle(style.timestamp)}
                          className="opacity-0 group-hover:opacity-100 transition-all duration-300 
                            text-gray-500 hover:text-gray-600 hover:bg-gray-50"
                          title="删除样式"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
        </div>
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => setIsOpen(false)}
            className="border-teal-200/40 hover:border-teal-400 text-teal-700 
              hover:text-teal-800 hover:bg-gray-100 hover:from-gray-50 hover:to-gray-50 
              transition-all duration-500 hover:-translate-y-[1px]"
          >
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 