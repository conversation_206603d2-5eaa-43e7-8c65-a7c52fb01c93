import { cn } from "@/lib/utils";

interface BackgroundEffectsProps {
  className?: string;
  isBaseResume?: boolean;
}

export function BackgroundEffects({ className, isBaseResume = true }: BackgroundEffectsProps) {
  return (
    <div className={cn("fixed inset-0 z-0  overflow-hidden  h-[calc(100vh)]", className)}>
      {/* 纯色背景 */}
      <div className={cn(
        "absolute inset-0",
        isBaseResume
          ? "bg-gray-50/30"
          : "bg-sky-50/50"
      )} />

      {/* 网格图案 */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,hsl(var(--border))_1px,transparent_1px),linear-gradient(to_bottom,hsl(var(--border))_1px,transparent_1px)] bg-[size:14px_24px]" />

      {/* 简化的装饰圆形 - 纯色设计 */}
      <div
        className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl animate-float-slow bg-gray-100/10"
        style={{ willChange: 'transform' }}
      />
      <div
        className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full blur-3xl animate-float-slower bg-gray-100/15"
        style={{ willChange: 'transform' }}
      />
    </div>
  );
}