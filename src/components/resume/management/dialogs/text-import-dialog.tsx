'use client';

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogTrigger, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Upload, AlertTriangle } from "lucide-react";
import { Resume } from "@/lib/types";

import { toast } from "@/hooks/use-toast";
import { addTextToResume } from "@/utils/actions/resumes/ai";
import pdfToText from "react-pdftotext";
import { cn } from "@/lib/utils";
import { ProUpgradeButton } from "@/components/settings/pro-upgrade-button";

interface TextImportDialogProps {
  resume: Resume;
  onResumeChange: (field: keyof Resume, value: Resume[keyof Resume]) => void;
  trigger: React.ReactNode;
}

export function TextImportDialog({
  resume,
  onResumeChange,
  trigger
}: TextImportDialogProps) {
  const [open, setOpen] = useState(false);
  const [content, setContent] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [apiKeyError, setApiKeyError] = useState("");

  useEffect(() => {
    if (!open) {
      setApiKeyError("");
    }
  }, [open]);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setIsDragging(true);
    } else if (e.type === "dragleave") {
      setIsDragging(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    const pdfFile = files.find(file => file.type === "application/pdf");

    if (pdfFile) {
      try {
        const text = await pdfToText(pdfFile);
        setContent(prev => prev + (prev ? "\n\n" : "") + text);
      } catch (err) {
        console.error('PDF processing error:', err);
        toast({
          title: "PDF Processing Error",
          description: "Failed to extract text from the PDF. Please try again or paste the content manually.",
          variant: "destructive",
        });
      }
    } else {
      toast({
        title: "Invalid File",
        description: "Please drop a PDF file.",
        variant: "destructive",
      });
    }
  };

  const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === "application/pdf") {
      try {
        const text = await pdfToText(file);
        setContent(prev => prev + (prev ? "\n\n" : "") + text);
      } catch (err) {
        console.error('PDF processing error:', err);
        toast({
          title: "PDF 处理错误",
          description: "无法从 PDF 中提取文本。请重试或手动粘贴内容。",
          variant: "destructive",
        });
      }
    }
  };

  const handleImport = async () => {
    setApiKeyError("");
    if (!content.trim()) {
      toast({
        title: "无内容",
        description: "请输入一些要导入的文本。",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      const updatedResume = await addTextToResume(content, resume);
      
      // Update each field of the resume
      (Object.keys(updatedResume) as Array<keyof Resume>).forEach((key) => {
        onResumeChange(key, updatedResume[key] as Resume[keyof Resume]);
      });

      toast({
        title: "Import successful",
        description: "Your resume has been updated with the imported content.",
      });
      setOpen(false);
      setContent("");
    } catch (error) {
      console.error('Import error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      if (errorMessage.includes('API key')) {
        setApiKeyError(
          'API key required. Please add your OpenAI API key in settings or upgrade to our Pro Plan.'
        );
      } else {
        toast({
          title: "Import failed",
          description: "Failed to process the text. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] bg-white/95 backdrop-blur-xl border-white/40 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl bg-gray-100 from-gray-600 to-gray-600 bg-clip-text text-transparent">
            导入简历内容
          </DialogTitle>
          <DialogDescription asChild>
            <div className="space-y-2 text-base text-muted-foreground/80">
              <p className="font-medium text-foreground">选择以下选项之一：</p>
              <ol className="list-decimal list-inside space-y-1 ml-1">
                <li>通过拖拽到下方或点击浏览上传您的 PDF 简历</li>
                <li>直接将简历文本粘贴到文本区域</li>
              </ol>
            </div>
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <label
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            className={cn(
              "border-2 border-dashed rounded-lg p-8 flex flex-col items-center justify-center gap-3 transition-colors duration-200 cursor-pointer group",
              isDragging
                ? "border-gray-500 bg-gray-50/50"
                : "border-gray-500/80 hover:border-gray-500 hover:bg-gray-50/10"
            )}
          >
            <input
              type="file"
              className="hidden"
              accept="application/pdf"
              onChange={handleFileInput}
            />
            <Upload className="w-10 h-10 text-gray-500 group-hover:scale-110 transition-transform duration-200" />
            <div className="text-center">
              <p className="text-sm font-medium text-foreground">
                拖拽您的 PDF 简历到此处
              </p>
              <p className="text-sm text-muted-foreground">
                或点击浏览文件
              </p>
            </div>
          </label>
          <div className="relative">
            <div className="absolute -top-3 left-3 bg-white px-2 text-sm text-muted-foreground">
              或粘贴您的简历文本到此处
            </div>
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="请在此粘贴您的简历内容..."
              className="min-h-[100px] bg-white/50 border-black/40 focus:border-gray-500/40 focus:ring-gray-500/20 transition-all duration-300 pt-4"
            />
          </div>
        </div>
        {apiKeyError && (
            <div className="px-4 py-3 bg-red-50/50 border border-red-200/50 rounded-lg flex items-start gap-3 text-red-600 text-sm">
              <div className="p-1.5 rounded-full bg-red-100">
                <AlertTriangle className="w-4 h-4 text-red-500" />
              </div>
              <div className="flex-1">
                <p className="font-medium">需要 API Key</p>
                <p className="text-red-500/90">{apiKeyError}</p>
                <div className="mt-2 flex flex-col gap-2 justify-start">
                  <div className="w-auto mx-auto">
                  <ProUpgradeButton />
                  </div>
                  <div className="text-center text-xs text-red-400">或</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-600 border-red-200 hover:bg-red-50/50 w-auto mx-auto"
                    onClick={() => window.location.href = '/plans'}
                  >
                    查看方案
                  </Button>
                </div>
              </div>
            </div>
        )}
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            className="border-gray-200"
          >
            取消
          </Button>
          <Button
            onClick={handleImport}
            disabled={isProcessing || !content.trim()}
            className="bg-gray-100 from-gray-600 to-gray-600 text-white hover:from-gray-700 hover:to-gray-700"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                正在处理...
              </>
            ) : (
              '导入'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 