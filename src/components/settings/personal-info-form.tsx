'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"

export function PersonalInfoForm() {
  return (
    <form className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="fullName">姓名</Label>
        <Input
          id="fullName"
          placeholder="请输入姓名"
          className="bg-white/50"
        />
      </div>

      <div className="flex justify-end">
        <Button 
          type="submit"
          className="bg-gray-100 from-gray-500 to-emerald-500 text-white hover:from-gray-600 hover:to-emerald-600"
        >
          <Loader2 className="mr-2 h-4 w-4 animate-spin opacity-0" />
          保存更改
        </Button>
      </div>
    </form>
  )
} 