'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Loader2 } from "lucide-react"
import { useFormStatus } from 'react-dom'
import { deleteUserAccount } from "@/app/auth/login/actions"

interface DangerZoneProps {
  subscriptionStatus?: string;
}

function SubmitButton() {
  const { pending } = useFormStatus()

  return (
    <AlertDialogAction
      type="submit"
      className="bg-destructive hover:bg-destructive/90 text-white"
      disabled={pending}
    >
      {pending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      删除账户
    </AlertDialogAction>
  )
}

export function DangerZone({ subscriptionStatus }: DangerZoneProps) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between p-4 rounded-lg border border-gray-200 bg-gray-50">
        <div>
          <h3 className="font-medium text-gray-900">删除账户</h3>
          <p className="text-sm text-gray-700">
            永久删除您的账户和所有数据
          </p>
          {subscriptionStatus === 'active' && (
            <p className="text-sm text-gray-700 mt-2">
              您当前有活跃的订阅。请先在上方取消订阅，然后再删除账户。
            </p>
          )}
        </div>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button 
              variant="outline"
              className="border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              disabled={subscriptionStatus === 'active'}
            >
              删除账户
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <form action={deleteUserAccount}>
              <AlertDialogHeader>
                <AlertDialogTitle>您确定要这样做吗？</AlertDialogTitle>
                <AlertDialogDescription>
                  此操作无法撤销。这将永久删除您的账户并从我们的服务器中移除您的数据。
                </AlertDialogDescription>
              </AlertDialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="confirm">输入 &ldquo;DELETE&rdquo; 确认</Label>
                  <Input
                    id="confirm"
                    name="confirm"
                    placeholder="DELETE"
                    className="bg-background/50"
                    required
                  />
                </div>
              </div>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <SubmitButton />
              </AlertDialogFooter>
            </form>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  )
} 