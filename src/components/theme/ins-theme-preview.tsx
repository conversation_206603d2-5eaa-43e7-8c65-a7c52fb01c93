'use client';

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export function InsThemePreview() {
  return (
    <div className="p-8 space-y-8 bg-background min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-600 mb-2">
          INS风格主题预览
        </h1>
        <p className="text-muted-foreground mb-8">
          舒适清新的Instagram风格色调，高级感满满
        </p>

        {/* 色彩展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-gray-700">薄荷绿系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-gray-100 rounded flex items-center px-3 text-xs">gray-100</div>
              <div className="h-8 bg-gray-300 rounded flex items-center px-3 text-xs">gray-300</div>
              <div className="h-8 bg-gray-500 rounded flex items-center px-3 text-xs text-white">gray-500</div>
              <div className="h-8 bg-gray-700 rounded flex items-center px-3 text-xs text-white">gray-700</div>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-gray-700">珊瑚橙系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-gray-100 rounded flex items-center px-3 text-xs">gray-100</div>
              <div className="h-8 bg-gray-300 rounded flex items-center px-3 text-xs">gray-300</div>
              <div className="h-8 bg-gray-500 rounded flex items-center px-3 text-xs text-white">gray-500</div>
              <div className="h-8 bg-gray-700 rounded flex items-center px-3 text-xs text-white">gray-700</div>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-gray-700">淡粉色系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-gray-100 rounded flex items-center px-3 text-xs">gray-100</div>
              <div className="h-8 bg-gray-300 rounded flex items-center px-3 text-xs">gray-300</div>
              <div className="h-8 bg-gray-500 rounded flex items-center px-3 text-xs text-white">gray-500</div>
              <div className="h-8 bg-gray-700 rounded flex items-center px-3 text-xs text-white">gray-700</div>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold text-gray-700">温暖色系列</h3>
            <div className="space-y-2">
              <div className="h-8 bg-gray-100 rounded flex items-center px-3 text-xs">gray-100</div>
              <div className="h-8 bg-gray-300 rounded flex items-center px-3 text-xs">gray-300</div>
              <div className="h-8 bg-gray-500 rounded flex items-center px-3 text-xs text-white">gray-500</div>
              <div className="h-8 bg-gray-700 rounded flex items-center px-3 text-xs text-white">gray-700</div>
            </div>
          </Card>
        </div>

        {/* 组件展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card className="p-6 space-y-4">
            <h3 className="font-semibold">按钮样式</h3>
            <div className="space-y-3">
              <Button className="w-full bg-gray-600 hover:bg-gray-700 text-white">
                主要按钮
              </Button>
              <Button variant="secondary" className="w-full">
                次要按钮
              </Button>
              <Button variant="outline" className="w-full">
                边框按钮
              </Button>
            </div>
          </Card>

          <Card className="p-6 space-y-4">
            <h3 className="font-semibold">纯色效果</h3>
            <div className="space-y-3">
              <div className="h-12 bg-gray-200 rounded flex items-center justify-center">
                浅色背景
              </div>
              <div className="h-12 bg-gray-600 rounded flex items-center justify-center text-white">
                中等背景
              </div>
              <div className="h-12 bg-gray-600 rounded flex items-center justify-center text-white">
                强调背景
              </div>
            </div>
          </Card>
        </div>

        {/* 背景效果展示 */}
        <Card className="p-6 space-y-4">
          <h3 className="font-semibold">背景效果</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="h-32 bg-solid-glow-mint rounded flex items-center justify-center">
              薄荷绿背景
            </div>
            <div className="h-32 bg-solid-glow-coral rounded flex items-center justify-center">
              珊瑚色背景
            </div>
            <div className="h-32 glass-card flex items-center justify-center">
              玻璃效果
            </div>
          </div>
        </Card>

        {/* 文字效果展示 */}
        <Card className="p-6 space-y-4">
          <h3 className="font-semibold">文字效果</h3>
          <div className="space-y-4">
            <h1 className="text-4xl font-bold text-gray-600">
              大标题纯色效果
            </h1>
            <h2 className="text-2xl font-semibold text-gray-600">
              薄荷绿标题
            </h2>
            <h3 className="text-xl font-medium text-gray-500">
              珊瑚色标题
            </h3>
            <p className="text-muted-foreground">
              这是一段普通的文字，展示了新主题的可读性和舒适度。
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
}
