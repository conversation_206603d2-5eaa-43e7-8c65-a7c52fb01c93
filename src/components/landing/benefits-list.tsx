'use client';

import { CheckCircle2 } from "lucide-react";

const BENEFITS = [
  "AI智能优化",
  "专注计算机科学/工程",
  "可定制模型和系统提示"
] as const;

export function BenefitsList() {
  return (
    <div className="flex flex-col gap-4 text-sm text-muted-foreground">
      {BENEFITS.map((benefit, i) => (
        <div key={i} className="flex items-center gap-3">
          <CheckCircle2 className="w-4 h-4 text-gray-600 flex-shrink-0" />
          <span>{benefit}</span>
        </div>
      ))}
    </div>
  );
} 