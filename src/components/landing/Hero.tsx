import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { AuthDialog } from "@/components/auth/auth-dialog";

export function Hero() {
  return (
    <section className="flex flex-col lg:flex-row items-center gap-8 lg:gap-16 min-h-[90vh] pt-4 lg:pt-6 -mt-12 -mb-12">
      {/* Left Content - 通过减少上边距来上移 */}
      <div className="w-full lg:w-1/2 space-y-5 flex flex-col justify-center">
        
        {/* Tagline with simplified gradient text */}
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight">
          <span className="block text-gray-900">闪电Offer，AI求职智能体</span>
          <span className="block text-gray-900">你的Offer收割机</span>
        </h1>

        {/* Description with quantifiable benefits */}
        <p className="text-lg md:text-xl text-muted-foreground max-w-md">
          快速为目标岗位定制简历和应聘攻略，帮助求职者评估岗位和获得攻略，快速拿下Offer。
        </p>
        
        {/* CTAs with simplified effects - 保持左对齐 */}
        <div className="flex justify-start mt-8">
          <AuthDialog>
            <button
              className="px-8 py-4 rounded-lg bg-blue-500 hover:bg-blue-600 text-white font-medium transition-all duration-300 hover:-translate-y-1 flex items-center justify-center text-lg"
              aria-label="Create your resume now"
            >
              <span>开始创建</span>
              <svg className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            </button>
          </AuthDialog>
        </div>
        
        {/* Feature badges with simplified styling */}
        <div className="flex flex-wrap gap-3 mt-6">
          <span className="px-3 py-1 rounded-full bg-muted text-sm border border-border text-foreground">智能求职</span>
          <span className="px-3 py-1 rounded-full bg-muted text-sm border border-border text-foreground">岗位分析</span>
          <span className="px-3 py-1 rounded-full bg-muted text-sm border border-border text-foreground">应聘攻略</span>
          <span className="px-3 py-1 rounded-full bg-muted text-sm border border-border text-foreground">Offer收割</span>
        </div>
        
        {/* Simplified social proof section */}
        <div className="mt-8">
          <div className="flex items-center p-4 rounded-xl bg-white border border-gray-200 shadow-sm transition-all duration-300 hover:-translate-y-1">
            {/* Stats highlight with simplified styling */}
            <div className="flex-shrink-0 mr-5">
              <div className="flex items-center justify-center h-16 w-16 rounded-full bg-gray-50 border border-gray-100">
                <span className="text-2xl font-bold text-foreground">500+</span>
              </div>
            </div>
            
            {/* Text content with testimonial */}
            <div className="flex-1">
              <h3 className="font-semibold text-base text-foreground">已帮助超过500位求职者获得Offer</h3>

              <p className="text-xs italic mt-1 text-muted-foreground">&ldquo;使用 SnapOffer 后快速获得理想Offer&rdquo; — 李明</p>
              
              {/* Shadcn Avatar stack */}
              <div className="flex items-center mt-3">
                <div className="flex -space-x-2 mr-3">
                  <Avatar className="h-7 w-7 border-2 border-background">
                    <AvatarFallback className="bg-gray-500 text-white text-xs">JD</AvatarFallback>
                  </Avatar>
                  <Avatar className="h-7 w-7 border-2 border-background">
                    <AvatarFallback className="bg-gray-500 text-white text-xs">SR</AvatarFallback>
                  </Avatar>
                  <Avatar className="h-7 w-7 border-2 border-background">
                    <AvatarFallback className="bg-gray-500 text-white text-xs">KL</AvatarFallback>
                  </Avatar>
                  <Avatar className="h-7 w-7 border-2 border-background">
                    <AvatarFallback className="bg-gray-500 text-white text-xs">MP</AvatarFallback>
                  </Avatar>
                  <Avatar className="h-7 w-7 border-2 border-background">
                    <AvatarFallback className="bg-white text-xs text-gray-600 font-medium">496+</AvatarFallback>
                  </Avatar>
                </div>
                <span className="text-xs text-muted-foreground">本月活跃用户</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Right Content - 恢复原始定位，整体上移 */}
      <div className="w-full lg:w-1/2 relative flex items-center justify-center -mt-24 lg:-mt-32">
        {/* Main resume mockup with simplified visuals */}
        <div className="relative w-full max-w-sm mx-auto aspect-[3/4] rounded-2xl bg-white border border-gray-200 shadow-md overflow-hidden transform transition-all duration-300 hover:-translate-y-2">
          {/* Resume header with simplified colors */}
          <div className="absolute top-0 left-0 w-full h-[15%] bg-gray-900">
            <div className="absolute top-6 left-8 w-[50%] h-[20%] bg-white/90 rounded-sm"></div>
            <div className="absolute bottom-0 left-8 w-[30%] h-[20%] bg-white/80 rounded-t-lg"></div>
          </div>
          
          {/* Resume content with simplified styling */}
          <div className="absolute top-[20%] left-8 w-[80%] h-[4%] bg-slate-200 rounded-md"></div>
          <div className="absolute top-[26%] left-8 w-[60%] h-[3%] bg-slate-200 rounded-md"></div>
          <div className="absolute top-[30%] left-8 w-[70%] h-[3%] bg-slate-200 rounded-md"></div>
          
          {/* Experience Section */}
          <div className="absolute top-[36%] left-8 w-[35%] h-[4%] bg-gray-100 rounded-md"></div>
          <div className="absolute top-[42%] left-8 w-[80%] h-[3%] bg-slate-200 rounded-md"></div>
          <div className="absolute top-[46%] left-8 w-[75%] h-[3%] bg-slate-200 rounded-md"></div>
          <div className="absolute top-[50%] left-8 w-[70%] h-[3%] bg-slate-200 rounded-md"></div>
          
          {/* Skills Section */}
          <div className="absolute top-[56%] left-8 w-[35%] h-[4%] bg-gray-100 rounded-md"></div>
          <div className="absolute top-[62%] right-8 flex flex-wrap gap-2 w-[80%]">
            <div className="h-[12px] w-[60px] bg-gray-100 rounded-full"></div>
            <div className="h-[12px] w-[70px] bg-gray-100 rounded-full"></div>
            <div className="h-[12px] w-[50px] bg-gray-100 rounded-full"></div>
            <div className="h-[12px] w-[80px] bg-gray-100 rounded-full"></div>
            <div className="h-[12px] w-[65px] bg-gray-100 rounded-full"></div>
          </div>
          
          {/* Education Section */}
          <div className="absolute top-[70%] left-8 w-[35%] h-[4%] bg-gray-100 rounded-md"></div>
          <div className="absolute top-[76%] left-8 w-[80%] h-[3%] bg-slate-200 rounded-md"></div>
          <div className="absolute top-[80%] left-8 w-[75%] h-[3%] bg-slate-200 rounded-md"></div>
          <div className="absolute top-[84%] left-8 w-[70%] h-[3%] bg-slate-200 rounded-md"></div>
          
          {/* AI optimization indicator */}
          <div className="absolute bottom-3 right-3 px-3 py-1.5 rounded-md bg-muted border border-border text-sm text-foreground font-medium">
            AI 优化
          </div>
        </div>
        
        {/* Tailored resume variant - 恢复原始定位 */}
        <div className="absolute -bottom-8 -left-6 w-[35%] aspect-[3/4] rounded-xl bg-white border border-gray-200 shadow-md overflow-hidden rotate-[-8deg] z-10 transition-all duration-300 hover:rotate-[-4deg]">
          <div className="w-full h-[10%] bg-gray-900">
            <div className="absolute top-2 left-2 w-[40%] h-[5%] bg-white/80 rounded-sm"></div>
          </div>
          <div className="absolute top-[15%] left-2 right-2 h-[80%] flex flex-col gap-1">
            <div className="h-[8px] w-[80%] bg-slate-200 rounded-sm"></div>
            <div className="h-[8px] w-[70%] bg-slate-200 rounded-sm"></div>
            <div className="mt-2 h-[8px] w-[50%] bg-gray-100 rounded-sm"></div>
            <div className="h-[8px] w-[80%] bg-slate-200 rounded-sm"></div>
            <div className="h-[8px] w-[75%] bg-slate-200 rounded-sm"></div>
          </div>
          <div className="absolute bottom-2 right-2 px-1.5 py-0.5 rounded bg-muted border border-border text-[8px] text-foreground">
            定制化
          </div>
        </div>
        
        {/* Technical role variant - simplified */}
        <div className="absolute -top-8 -right-4 w-[35%] aspect-[3/4] rounded-xl bg-white border border-gray-200 shadow-md overflow-hidden rotate-[8deg] z-10 transition-all duration-300 hover:rotate-[4deg]">
          <div className="w-full h-[10%] bg-gray-900">
            <div className="absolute top-2 left-2 w-[40%] h-[5%] bg-white/80 rounded-sm"></div>
          </div>
          <div className="absolute top-[15%] left-2 right-2 h-[80%] flex flex-col gap-1">
            <div className="h-[8px] w-[80%] bg-slate-200 rounded-sm"></div>
            <div className="h-[8px] w-[70%] bg-slate-200 rounded-sm"></div>
            <div className="mt-2 h-[8px] w-[50%] bg-gray-100 rounded-sm"></div>
            <div className="h-[8px] w-[80%] bg-slate-200 rounded-sm"></div>
            <div className="h-[8px] w-[75%] bg-slate-200 rounded-sm"></div>
          </div>
          <div className="absolute bottom-2 right-2 px-1.5 py-0.5 rounded bg-muted border border-border text-[8px] text-foreground">
            技术型
          </div>
        </div>
      </div>
    </section>
  );
} 