"use client"

import React, { useRef } from 'react';
import { motion, useInView } from "framer-motion";
import { HelpCircle, Sparkles } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { designTokens, designTokenUtils } from "@/lib/design-tokens";

interface FAQItem {
  question: string;
  answer: string;
}

export function FAQ() {
  // Refs for intersection observer
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  // FAQ data - focused on SnapOffer specific questions
  const faqItems: FAQItem[] = [
    {
      question: "SnapOffer 的 AI 如何为特定岗位定制我的应聘策略？",
      answer: "我们的 AI 智能分析岗位要求，自动为您定制最合适的简历和应聘攻略。它会评估岗位竞争力，提供个性化改进建议，预测面试问题，帮助您快速获得Offer。"
    },
    {
      question: "SnapOffer 真的免费使用吗？",
      answer: "是的！我们的免费计划包括2份基础简历和4份使用 GPT-4.1 Nano 模型的定制简历。我们的专业版（$20/月）提供无限简历和高级 AI 模型访问，如 Claude、GPT-4 和 Gemini。"
    },
    {
      question: "SnapOffer 与其他求职工具有什么不同？",
      answer: "SnapOffer 是 AI 求职智能体，不仅提供简历构建，还包括岗位申请管理、智能应聘攻略等功能。我们提供完整的求职管理解决方案，帮助您快速理解和评估岗位，快速获得Offer。"
    },
    {
      question: "使用 SnapOffer 获得Offer需要多长时间？",
      answer: "大多数用户在3天内就能获得理想Offer。我们的 AI 智能分析岗位要求，为您定制简历和应聘攻略，大幅提高获得Offer的概率。"
    },
    {
      question: "我的简历能通过 ATS（申请人跟踪系统）吗？",
      answer: "当然可以！SnapOffer 专门设计用于创建 ATS 优化的简历和应聘材料。我们的模板使用正确的格式、关键词优化和结构，ATS 系统可以轻松解析并给予高排名。"
    },
    {
      question: "有哪些 AI 模型可用？",
      answer: "免费用户可以访问我们的 GPT-4.1 Nano 模型进行基本 AI 功能。专业版订阅者可以访问高级模型，包括 Claude、GPT-4、Gemini 等，以增强岗位分析、简历优化和应聘攻略生成。"
    },
    {
      question: "我的数据安全和隐私吗？",
      answer: "您的隐私是我们的首要任务。所有数据都经过加密，您甚至可以自托管 SnapOffer 以获得完全控制。我们绝不会与第三方分享您的个人信息或简历数据。"
    },
    {
      question: "你们为学生或转行者提供支持吗？",
      answer: "当然！SnapOffer 非常适合学生、转行者和任何级别的专业人士。我们的 AI 帮助分析岗位要求，提供个性化应聘策略，无论您的经验水平如何，都能帮助您快速获得Offer。"
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="py-12 md:py-16 px-4 relative overflow-hidden scroll-mt-20" 
      id="faq"
      aria-labelledby="faq-heading"
    >
      {/* Simplified background decoration - 使用设计令牌 */}
      <div aria-hidden="true" className="absolute -top-32 -right-32 w-64 h-64 rounded-full blur-3xl" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.15) }}></div>
      <div aria-hidden="true" className="absolute -bottom-32 -left-32 w-64 h-64 rounded-full blur-3xl" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.15) }}></div>
      
      {/* Compact Heading Section */}
      <div className="relative z-10 max-w-2xl mx-auto text-center mb-12">
        {/* Badge */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
          transition={{ duration: 0.6 }}
          className="flex justify-center mb-3"
        >
          <span className="px-3 py-1 rounded-full text-sm flex items-center gap-2" style={{ 
            backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.5),
            border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.4)}`,
            color: designTokens.colors.gray[700]
          }}>
            <HelpCircle className="w-4 h-4" />
            FAQ
          </span>
        </motion.div>
        
        {/* Compact heading */}
        <motion.h2
          id="faq-heading"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="text-3xl md:text-4xl font-bold tracking-tight mb-3"
          style={{ color: designTokens.colors.gray[900] }}
        >
          常见问题
        </motion.h2>
        
        {/* Shorter description */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-base md:text-lg"
          style={{ color: designTokens.colors.gray[600] }}
        >
          快速解答帮助您开始使用 SnapOffer
        </motion.p>
      </div>
      
      {/* Compact FAQ Accordion */}
      <motion.div
        className="relative z-10 max-w-3xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
      >
        <Accordion type="single" collapsible className="space-y-2">
          {faqItems.map((item, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <AccordionItem 
                value={`item-${index}`} 
                className="rounded-lg bg-white/40 backdrop-blur-sm hover:bg-white/60 transition-all duration-200 hover:shadow-sm px-4 py-1"
                style={{ border: `1px solid ${designTokenUtils.withOpacity(designTokens.colors.gray[200], 0.5)}` }}
              >
                <AccordionTrigger className="text-left hover:no-underline group-hover:text-gray-700 transition-colors duration-200 py-4 text-sm md:text-base font-medium" style={{ color: designTokens.colors.gray[800] }}>
                  <span className="flex items-start gap-2">
                    <Sparkles className="w-4 h-4 mt-0.5 flex-shrink-0 opacity-60 group-hover:opacity-100 transition-opacity duration-200" style={{ color: designTokens.colors.gray[600] }} />
                    {item.question}
                  </span>
                </AccordionTrigger>
                <AccordionContent className="leading-relaxed pb-4 pl-6 text-sm" style={{ color: designTokens.colors.gray[600] }}>
                  {item.answer}
                </AccordionContent>
              </AccordionItem>
            </motion.div>
          ))}
                </Accordion>
      </motion.div>
    </section>
  );
} 