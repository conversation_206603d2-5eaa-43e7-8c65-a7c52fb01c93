import { designTokens, designTokenUtils } from "@/lib/design-tokens";

export function Background() {
  return (
    <div className="fixed inset-0 z-0 overflow-hidden size-full">
      {/* 简洁的纯色背景 - 使用设计令牌 */}
      <div className="absolute inset-0" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[50], 0.3) }} />

      {/* 微妙的装饰圆形 - 纯色设计 - 使用设计令牌 */}
      <div className="absolute top-1/3 left-1/2 w-[400px] h-[400px] rounded-full blur-2xl animate-pulse opacity-40 transform -translate-x-1/2" style={{ backgroundColor: designTokenUtils.withOpacity(designTokens.colors.gray[100], 0.2) }} />

      {/* 简化的网格覆盖层 - 使用设计令牌 */}
      <div className="absolute inset-0" style={{ 
        backgroundImage: `linear-gradient(to right, ${designTokens.colors.gray[200]} 1px, transparent 1px), linear-gradient(to bottom, ${designTokens.colors.gray[200]} 1px, transparent 1px)`,
        backgroundSize: '20px 20px'
      }} />
    </div>
  );
}