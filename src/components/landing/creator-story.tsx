"use client"
import Image from "next/image";
import { motion } from "framer-motion";

export function CreatorStory() {
  return (
    <section className="py-16 md:py-20 relative overflow-hidden" id="creator-story">
      {/* Simplified background elements */}
      <div className="absolute top-0 left-0 w-96 h-96 rounded-full bg-gray-100/10"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 rounded-full bg-teal-100/10"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section heading with simplified styling */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          className="text-center mb-16"
        >
          <motion.span
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="px-4 py-1.5 rounded-full bg-gray-50 border border-gray-200 text-sm text-gray-700 inline-block mb-4"
          >
            SnapOffer 背后的故事
          </motion.span>
          <h2 className="text-4xl md:text-5xl font-bold tracking-tight text-gray-900">
            认识创作者
          </h2>
        </motion.div>
        
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="grid md:grid-cols-[400px_1fr] gap-10 items-center"
        >
          {/* Image Area with simplified styling */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="relative mx-auto md:mx-0"
          >
            <div className="relative aspect-square w-64 md:w-96 rounded-2xl overflow-hidden bg-white border border-gray-200 shadow-md transform transition-all duration-300 hover:-translate-y-2">
              <Image
                src="/alex.webp"
                alt="Alex, creator of SnapOffer"
                fill
                sizes="(max-width: 768px) 256px, 384px"
                className="object-cover"
                priority
              />
            </div>
          </motion.div>

          {/* Story Content with simplified styling */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, delay: 0.5 }}
            className="relative"
          >
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold tracking-tight sm:text-3xl text-gray-800">
                Why I Built SnapOffer
              </h2>
              
              <div className="space-y-4 text-lg text-slate-800 leading-relaxed">
                <p>
                  大家好，我们是一支来自中国的年轻开发团队，成员分布于北京、上海、深圳等地，拥有丰富的互联网和AI产品开发经验。
                </p>

                <p>
                  我们打造了 SnapOffer —— 一个AI求职智能体，致力于帮助中国及全球的求职者快速获得理想Offer。通过先进的AI技术，我们提供完整的求职管理解决方案，包括简历构建、岗位申请管理、智能应聘攻略等功能。
                </p>

              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
} 