'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";

interface ApiErrorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  errorMessage: { title: string; description: string };
  onUpgrade: () => void;
  onSettings: () => void;
}

export function ApiErrorDialog({ 
  open, 
  onOpenChange,
  errorMessage,
  onUpgrade,
  onSettings
}: ApiErrorDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className={cn(
        "bg-background/95 backdrop-blur-xl",
        "border-2 border-input/40",
        "p-0 gap-0 pb-8"
      )}>
        <div className={cn(
          "flex flex-col items-center gap-4 p-6",
          "text-muted-foreground text-center"
        )}>
          <div className="flex flex-col items-center gap-3">
            <div className="p-3 rounded-full bg-muted">
              <AlertTriangle className="w-6 h-6 text-muted-foreground" />
            </div>
            <AlertDialogTitle className="text-foreground text-lg">
              {errorMessage.title}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-foreground">
              {errorMessage.description}
            </AlertDialogDescription>
          </div>

          <div className="w-full h-px bg-muted" />

          <div className="text-sm text-muted-foreground mb-2">
            Unlock premium features and advanced AI capabilities
          </div>
          
          <div className="flex flex-col items-center gap-2 w-full">
            <div className="relative group w-full">
              <div className="absolute -inset-[3px] bg-primary/0 rounded-lg opacity-75 blur-md group-hover:bg-primary/30 transition-all duration-300 ease-in-out" />

              <Button
                onClick={onUpgrade}
                className={cn(
                  "relative w-full",
                  "bg-primary hover:bg-primary/90",
                  "text-primary-foreground font-medium",
                  "shadow-lg hover:shadow-xl hover:shadow-primary/20",
                  "transition-all duration-300 ease-in-out",
                  "hover:-translate-y-0.5",
                  "flex items-center justify-center gap-1.5"
                )}
              >
                <Sparkles className="h-4 w-4 transition-transform duration-300 ease-in-out group-hover:scale-110" />
                <span className="transition-all duration-300 ease-in-out group-hover:translate-x-0.5">
                  Upgrade to Pro
                </span>
              </Button>
            </div>
            
            <Button
              variant="outline"
              onClick={() => window.open('/plans', '_blank')}
              className={cn(
                "w-full",
                "text-xs text-muted-foreground hover:text-foreground",
                "hover:bg-muted/50",
                "border border-input",
                "h-8",
                "transition-all duration-300",
                "hover:-translate-y-0.5",
                "hover:shadow-sm"
              )}
            >
              View Plans
            </Button>
            
            <Button
              variant="outline"
              onClick={onSettings}
              className={cn(
                "w-full",
                "text-xs text-muted-foreground hover:text-foreground",
                "hover:bg-muted/50",
                "border border-input",
                "h-8",
                "transition-all duration-300",
                "hover:-translate-y-0.5",
                "hover:shadow-sm"
              )}
            >
              Settings
            </Button>
          </div>

          <div className="absolute top-2 right-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className={cn(
                " text-xs text-muted-foreground hover:text-foreground",
                "h-7 px-2",
                "hover:bg-muted/50",
                "transition-colors duration-200 border border-input bg-muted" 
              )}
            >
              Dismiss
            </Button>
          </div>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
} 