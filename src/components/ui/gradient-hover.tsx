'use client';

import { cn } from "@/lib/utils";
import { designTokens } from "@/lib/design-tokens";

interface GradientHoverProps {
  children: React.ReactNode;
  className?: string;
  from?: string;
  via?: string;
  to?: string;
  hoverFrom?: string;
  hoverVia?: string;
  hoverTo?: string;
}

export function GradientHover({
  children,
  className,
  from = "blue.500",
  via = "blue.500",
  to = "blue.500",
  hoverFrom = "blue.600",
  hoverVia = "blue.600",
  hoverTo = "blue.600",
}: GradientHoverProps) {
  // 解析颜色路径
  const getColor = (colorPath: string) => {
    const parts = colorPath.split('.');
    return parts.reduce((obj, key) => obj?.[key], designTokens.colors) as string;
  };

  const fromColor = getColor(from);
  const viaColor = getColor(via);
  const toColor = getColor(to);
  const hoverFromColor = getColor(hoverFrom);
  const hoverViaColor = getColor(hoverVia);
  const hoverToColor = getColor(hoverTo);

  return (
    <span
      className={cn(
        "transition-all duration-300 cursor-pointer",
        "bg-clip-text text-transparent",
        className
      )}
      style={{
        backgroundImage: `linear-gradient(to right, ${fromColor}, ${viaColor}, ${toColor})`,
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundImage = `linear-gradient(to right, ${hoverFromColor}, ${hoverViaColor}, ${hoverToColor})`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundImage = `linear-gradient(to right, ${fromColor}, ${viaColor}, ${toColor})`;
      }}
    >
      {children}
    </span>
  );
}