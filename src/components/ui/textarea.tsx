import * as React from "react"

import { cn } from "@/lib/utils"

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea">
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        // Base styles
        "flex w-full rounded-xl border-2 bg-background px-4 py-3 text-sm",
        "border-input",
        "shadow-sm shadow-muted/30",
        "placeholder:text-muted-foreground/60",
        // Interactive states
        "hover:border-input hover:bg-background",
        "focus:border-primary/60 focus:bg-background focus:ring-4 focus:ring-primary/10 focus:ring-offset-0",
        "focus-visible:outline-none",
        // Disabled state
        "disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:bg-background/90",
        // Custom scrollbar
        "custom-scrollbar",
        className
      )}
      ref={ref}
      {...props}
    />
  )
})
Textarea.displayName = "Textarea"

export { Textarea }
