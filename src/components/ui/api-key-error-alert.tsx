import { <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ProUpgradeButton } from "@/components/settings/pro-upgrade-button";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";

interface ApiKeyErrorAlertProps {
  error: unknown;
  router: ReturnType<typeof useRouter>;
}

export function ApiKeyErrorAlert({ error, router }: ApiKeyErrorAlertProps) {
  // Detailed console debugging
  console.group('🔍 ApiKeyErrorAlert Debug');
  console.log('Raw error:', error);
  console.log('Error type:', typeof error);
  console.log('Error constructor:', error?.constructor?.name);
  
  if (error instanceof Error) {
    console.log('Error message:', error.message);
    console.log('Error stack:', error.stack);
    console.log('Error name:', error.name);
  }
  
  try {
    console.log('Error stringified:', JSON.stringify(error, null, 2));
  } catch (e) {
    console.log('Error cannot be stringified:', e);
  }
  
  // Check specific conditions
  const errorString = typeof error === 'string' ? error : (error as Error)?.message || '';
  const errorJson = JSON.stringify(error);
  
  console.log('Error string for checking:', errorString);
  console.log('Error JSON for checking:', errorJson);
  console.log('Contains OpenAI API key not found:', errorString.includes('OpenAI API key not found') || errorJson.includes('OpenAI API key not found'));
  console.log('Contains invalid x-api-key:', errorString.includes('invalid x-api-key') || errorJson.includes('authentication_error'));
  console.log('Contains Incorrect API key:', errorString.includes('Incorrect API key provided') || errorJson.includes('invalid_api_key'));
  console.log('Contains Rate limit:', errorString.includes('Rate limit exceeded') || errorJson.includes('Rate limit exceeded'));
  console.groupEnd();

  return (
    <div className={cn(
      "mt-2 text-sm px-4",
      "rounded-lg py-2",
      "bg-destructive/10 border border-destructive/20",
      "flex flex-col gap-2"
    )}>
      <div className={cn(
        "flex flex-col items-center gap-4 p-6",
        "text-destructive text-center",
        "bg-background/80 backdrop-blur-md",
        "rounded-xl",
        "border border-destructive/20",
        "shadow-sm"
      )}>
        <div className="flex flex-col items-center gap-3">
          <div className="p-3 rounded-full bg-destructive/10">
            <AlertTriangle className="w-6 h-6 text-destructive" />
          </div>
          <div className="font-medium text-destructive">
            {typeof error === 'string' 
              ? error
              : ((error as Error)?.name === 'DataCloneError' || 
                  (error as Error)?.message?.includes('structuredClone') ||
                  (error as Error)?.message?.includes('could not be cloned'))
                  ? "处理响应时出现问题。请重试您的请求。"
                  : ((error as Error)?.message?.includes('API key') ||
                      JSON.stringify(error).includes('API key') ||
                      JSON.stringify(error).includes('authentication_error'))
                      ? "请升级到专业版以使用高级 AI 模型。"
                              : ((error as Error)?.message?.includes('Rate limit exceeded') ||
                                  JSON.stringify(error).includes('Rate limit exceeded'))
                                  ? `您已超出速率限制。请在 ${(() => {
                                      try {
                                        const errorData = JSON.parse((error as Error).message);
                                        const expiration = errorData.expirationTimestamp 
                                          ? new Date(errorData.expirationTimestamp)
                                          : new Date(Date.now() + ((errorData.timeLeft || 30) * 1000));
                                        
                                        return expiration.toLocaleTimeString([], { 
                                          hour: 'numeric', 
                                          minute: '2-digit',
                                          hour12: true 
                                        });
                                      } catch {
                                        const fallbackTime = new Date(Date.now() + 30_000);
                                        return fallbackTime.toLocaleTimeString([], {
                                          hour: 'numeric',
                                          minute: '2-digit',
                                          hour12: true
                                        });
                                      }
                                    })()}} 后重试。升级到专业版以获得更高限制。`
                                  : "发生错误。请重试或检查您的设置。"}
          </div>
        </div>

        {((error as Error)?.message?.includes('API key') || 
          JSON.stringify(error).includes('API key') || 
          JSON.stringify(error).includes('authentication_error')) &&
          !((error as Error)?.name === 'DataCloneError' || 
            (error as Error)?.message?.includes('structuredClone') ||
            (error as Error)?.message?.includes('could not be cloned')) ? (
          <>
            <div className="w-full h-px bg-destructive/10" />
            <div className="text-sm text-muted-foreground mb-2">
              解锁高级功能和先进的 AI 能力
            </div>
            <div className="flex flex-col items-center gap-2 w-full">
              <ProUpgradeButton />
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "text-xs text-muted-foreground hover:text-foreground",
                  "hover:bg-muted/50 bg-muted",
                  "border border-input",
                  "h-8"
                )}
                onClick={() => router.push('/plans')}
              >
                View Plans
              </Button>
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
} 