'use client';

import { useEffect, useRef } from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InfiniteScrollTriggerProps {
  /** 当触发器进入视口时调用的函数 */
  onIntersect: () => void;
  /** 是否正在加载更多数据 */
  isLoading?: boolean;
  /** 是否还有更多数据可加载 */
  hasMore?: boolean;
  /** 触发加载的距离阈值（0-1，表示元素可见的百分比） */
  threshold?: number;
  /** 提前触发的根边距（像素） */
  rootMargin?: string;
  /** 自定义类名 */
  className?: string;
  /** 加载状态的自定义内容 */
  loadingContent?: React.ReactNode;
  /** 无更多数据时的自定义内容 */
  noMoreContent?: React.ReactNode;
}

/**
 * 无限滚动触发器组件
 * 使用 Intersection Observer API 检测用户滚动到底部并触发加载更多数据
 */
export function InfiniteScrollTrigger({
  onIntersect,
  isLoading = false,
  hasMore = true,
  threshold = 0.1,
  rootMargin = '100px',
  className,
  loadingContent,
  noMoreContent
}: InfiniteScrollTriggerProps) {
  const triggerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    const triggerElement = triggerRef.current;
    if (!triggerElement) return;

    // 创建 Intersection Observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        
        // 当触发器进入视口且还有更多数据且不在加载中时，触发加载
        if (entry.isIntersecting && hasMore && !isLoading) {
          onIntersect();
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    // 开始观察
    observerRef.current.observe(triggerElement);

    // 清理函数
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [onIntersect, hasMore, isLoading, threshold, rootMargin]);

  // 如果没有更多数据，显示结束提示
  if (!hasMore) {
    return (
      <div 
        ref={triggerRef}
        className={cn(
          "flex items-center justify-center py-8 text-muted-foreground",
          className
        )}
      >
        {noMoreContent || (
          <div className="text-center">
            <div className="text-sm">已显示全部职位</div>
          </div>
        )}
      </div>
    );
  }

  // 显示加载状态
  return (
    <div 
      ref={triggerRef}
      className={cn(
        "flex items-center justify-center py-8",
        className
      )}
    >
      {isLoading ? (
        loadingContent || (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm">加载更多职位...</span>
          </div>
        )
      ) : (
        // 不在加载状态时显示空内容，但保持元素存在以便 Observer 检测
        <div className="h-1" />
      )}
    </div>
  );
}
