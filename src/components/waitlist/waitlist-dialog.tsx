'use client';

import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowRight, Sparkles, Mail, User } from "lucide-react";
import { Logo } from "@/components/ui/logo";

const solidClasses = {
  base: "bg-gray-600",
  hover: "hover:bg-gray-700",
  shadow: "shadow-lg shadow-gray-500/25",
  animation: "transition-all duration-300",
};

export function WaitlistDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          size="lg" 
          className={`${solidClasses.base} ${solidClasses.hover} ${solidClasses.shadow} px-8 ${solidClasses.animation} group`}
        >
          加入等候名单
          <ArrowRight className="ml-2.5 w-4 h-4 group-hover:translate-x-0.5 transition-transform" />
        </Button>
      </DialogTrigger>

      <DialogContent 
        className="sm:max-w-[500px] p-0 bg-white/95 border-white/40 shadow-2xl animate-in fade-in-0 zoom-in-95"
      >
        <div className="px-8 pt-8 pb-0 text-center relative">
          <div className="absolute top-0 left-0 w-full h-32 bg-gray-100 from-gray-600/5 via-gray-600/5 to-gray-600/5 blur-2xl" />
          <div className="relative">
            <div className="inline-flex items-center justify-center space-x-2 mb-3">
              <Sparkles className="w-5 h-5 text-gray-500" aria-hidden="true" />
              <span className="text-sm font-medium text-gray-600">
                抢先体验等候名单
              </span>
            </div>
            <DialogTitle className="text-center">
              <Logo className="text-4xl mb-3" asLink={false} />
            </DialogTitle>
            <p className="text-muted-foreground text-sm max-w-md mx-auto">
              加入我们的等候名单，成为首批体验我们 AI 求职智能体的用户，
              并获得独家抢先体验权益。
            </p>
          </div>
        </div>

        <div className="p-8 relative">
          {/* Decorative background elements */}
          <div 
            className="absolute inset-0 bg-gray-100 from-gray-100/20 via-transparent to-gray-100/20 rounded-b-lg"
            aria-hidden="true"
          />
          
          {/* Form */}
          <form className="relative z-20 space-y-6">
            <div className="space-y-2">
              <Label htmlFor="firstName" className="text-sm font-medium">
                姓名
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground/50" />
                <Input
                  id="firstName"
                  placeholder="请输入姓名"
                  className="pl-9 bg-white/60 border-white/40 focus:border-gray-500 transition-colors"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                邮箱地址
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground/50" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="pl-9 bg-white/60 border-white/40 focus:border-gray-500 transition-colors"
                />
              </div>
            </div>

            <Button 
              type="submit"
              className={`w-full ${solidClasses.base} ${solidClasses.hover} ${solidClasses.shadow} ${solidClasses.animation} group h-11`}
            >
              加入等候名单
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-0.5 transition-transform" />
            </Button>

            <div className="space-y-3 text-center">
              <p className="text-xs text-muted-foreground">
                我们会在发布时通知您。无垃圾邮件，只有更新！
              </p>
              <div className="flex items-center justify-center gap-3">
                <div className="h-px flex-1 bg-gray-100 from-transparent via-muted-foreground/20 to-transparent" />
                <span className="text-xs text-muted-foreground/50">抢先体验权益</span>
                <div className="h-px flex-1 bg-gray-100 from-transparent via-muted-foreground/20 to-transparent" />
              </div>
              <div className="flex justify-center gap-4 text-xs text-muted-foreground">
                <span className="flex items-center">
                  <Sparkles className="mr-1.5 w-3 h-3 text-gray-500" />
                  优先访问
                </span>
                <span className="flex items-center">
                  <Sparkles className="mr-1.5 w-3 h-3 text-blue-500" />
                  延长试用
                </span>
                <span className="flex items-center">
                  <Sparkles className="mr-1.5 w-3 h-3 text-gray-500" />
                  特惠价格
                </span>
              </div>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
} 