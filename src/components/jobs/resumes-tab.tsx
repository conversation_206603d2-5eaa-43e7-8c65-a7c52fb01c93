'use client';

import { useEffect, useState } from 'react';
import { FileText, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Resume } from '@/lib/types';
import { getResumesByJobId } from '@/utils/actions/jobs/actions';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface ResumesTabProps {
  jobId: string;
  onGenerateResume?: () => void;
}

/**
 * 简历Tab组件 - 显示与职位关联的简历列表
 * Task 5.1: 读取 getResumesByJobId 并实现网格渲染
 * Task 5.2: 实现空态按钮占位并冒泡到父层处理
 */
export function ResumesTab({ jobId, onGenerateResume }: ResumesTabProps) {
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 获取关联简历数据
  useEffect(() => {
    async function fetchResumes() {
      try {
        setIsLoading(true);
        const resumeList = await getResumesByJobId(jobId);
        setResumes(resumeList);
      } catch (error) {
        console.error('获取简历列表失败:', error);
        toast({
          title: '获取简历列表失败',
          description: error instanceof Error ? error.message : '未知错误',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    }

    if (jobId) {
      fetchResumes();
    }
  }, [jobId]);

  // 处理简历操作
  const handleViewResume = (resumeId: string) => {
    // 在新标签页打开简历编辑器
    window.open(`/resumes/${resumeId}`, '_blank');
  };



  // 简历卡片组件
  const ResumeCard = ({ resume }: { resume: Resume }) => (
    <div 
      className="group relative overflow-hidden rounded-lg border border-gray-300 bg-white transition-all duration-200 hover:border-blue-400 hover:shadow-md hover:-translate-y-0.5 cursor-pointer"
      onClick={() => handleViewResume(resume.id)}
    >
      <div className="p-4 h-full flex flex-col">
        {/* 卡片头部 */}
        <div className="flex-1 flex flex-col justify-between">
          {/* 简历标题 */}
          <div className="mb-3">
            <h3 className="text-base font-semibold text-gray-900 line-clamp-2 leading-5">
              {resume.name || '未命名简历'}
            </h3>
          </div>
          
          {/* 底部信息 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs px-2 py-1 font-medium">
                定制简历
              </Badge>
              <span className="text-xs text-gray-500">
                {format(new Date(resume.updated_at || resume.created_at), 'yyyy/MM/dd')}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 空态组件
  const EmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
      <div className="w-16 h-16 rounded-full bg-blue-50 flex items-center justify-center mb-4">
        <FileText className="h-8 w-8 text-blue-500" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        暂无关联简历
      </h3>
      <p className="text-gray-600 mb-6 max-w-sm">
        为这个职位生成定制简历，让您的申请更具针对性和竞争力
      </p>
      <Button
        onClick={onGenerateResume}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        生成定制简历
      </Button>
    </div>
  );

  // 加载状态
  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-32" />
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* 头部信息 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-medium text-gray-900">定制简历</h3>
          {resumes.length > 0 && (
            <Badge variant="outline" className="text-sm">
              {resumes.length}
            </Badge>
          )}
        </div>
        
        {/* 当有简历时显示的操作按钮 */}
        {resumes.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={onGenerateResume}
          >
            <Plus className="h-4 w-4 mr-2" />
            再生成一份
          </Button>
        )}
      </div>

      {/* 简历列表或空态 */}
      {resumes.length === 0 ? (
        <EmptyState />
      ) : (
        <div className={cn(
          "grid gap-4",
          // 正确的响应式网格：小屏1列，中屏2列，大屏3列
          "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
          // 更合理的卡片高度比例，类似骨架屏
          "[&>*]:h-[140px]"
        )}>
          {resumes.map((resume) => (
            <ResumeCard key={resume.id} resume={resume} />
          ))}
        </div>
      )}
    </div>
  );
}
