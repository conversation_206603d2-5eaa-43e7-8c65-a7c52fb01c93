/**
 * 自定义阶段集合计算单元测试
 * 测试从日志事件中计算当前活跃自定义阶段的逻辑
 */

import { calculateActiveCustomStages } from '../stage-selector';

interface ApplicationEvent {
  type: 'status_changed' | 'custom_stage_add' | 'custom_stage_remove' | 'note_add' | 'note_remove';
  from?: string;
  to?: string;
  name?: string;
  id?: string;
  note?: string;
  created_at: string;
}

describe('自定义阶段集合计算', () => {
  test('空日志应返回空数组', () => {
    const result = calculateActiveCustomStages([]);
    expect(result).toEqual([]);
  });

  test('仅添加事件应返回相应阶段', () => {
    const log: ApplicationEvent[] = [
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-01T00:00:00Z'
      },
      {
        type: 'custom_stage_add',
        name: '背景调查',
        created_at: '2023-01-02T00:00:00Z'
      }
    ];
    
    const result = calculateActiveCustomStages(log);
    expect(result).toEqual(['技术测试', '背景调查']);
  });

  test('添加后删除的阶段不应出现在结果中', () => {
    const log: ApplicationEvent[] = [
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-01T00:00:00Z'
      },
      {
        type: 'custom_stage_add',
        name: '背景调查',
        created_at: '2023-01-02T00:00:00Z'
      },
      {
        type: 'custom_stage_remove',
        name: '技术测试',
        created_at: '2023-01-03T00:00:00Z'
      }
    ];
    
    const result = calculateActiveCustomStages(log);
    expect(result).toEqual(['背景调查']);
  });

  test('重复添加相同阶段应只保留一个', () => {
    const log: ApplicationEvent[] = [
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-01T00:00:00Z'
      },
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-02T00:00:00Z'
      }
    ];
    
    const result = calculateActiveCustomStages(log);
    expect(result).toEqual(['技术测试']);
  });

  test('删除不存在的阶段不应影响结果', () => {
    const log: ApplicationEvent[] = [
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-01T00:00:00Z'
      },
      {
        type: 'custom_stage_remove',
        name: '不存在的阶段',
        created_at: '2023-01-02T00:00:00Z'
      }
    ];
    
    const result = calculateActiveCustomStages(log);
    expect(result).toEqual(['技术测试']);
  });

  test('删除后重新添加的阶段应出现在结果中', () => {
    const log: ApplicationEvent[] = [
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-01T00:00:00Z'
      },
      {
        type: 'custom_stage_remove',
        name: '技术测试',
        created_at: '2023-01-02T00:00:00Z'
      },
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-03T00:00:00Z'
      }
    ];
    
    const result = calculateActiveCustomStages(log);
    expect(result).toEqual(['技术测试']);
  });

  test('混合其他类型事件不应影响自定义阶段计算', () => {
    const log: ApplicationEvent[] = [
      {
        type: 'status_changed',
        from: '申请',
        to: '面试',
        created_at: '2023-01-01T00:00:00Z'
      },
      {
        type: 'custom_stage_add',
        name: '技术测试',
        created_at: '2023-01-02T00:00:00Z'
      },
      {
        type: 'note_add',
        id: 'note-1',
        note: '测试笔记',
        created_at: '2023-01-03T00:00:00Z'
      },
      {
        type: 'custom_stage_add',
        name: '背景调查',
        created_at: '2023-01-04T00:00:00Z'
      }
    ];
    
    const result = calculateActiveCustomStages(log);
    expect(result).toEqual(['技术测试', '背景调查']);
  });

  test('复杂场景：多次添加删除操作', () => {
    const log: ApplicationEvent[] = [
      { type: 'custom_stage_add', name: 'A', created_at: '2023-01-01T00:00:00Z' },
      { type: 'custom_stage_add', name: 'B', created_at: '2023-01-02T00:00:00Z' },
      { type: 'custom_stage_add', name: 'C', created_at: '2023-01-03T00:00:00Z' },
      { type: 'custom_stage_remove', name: 'B', created_at: '2023-01-04T00:00:00Z' },
      { type: 'custom_stage_add', name: 'D', created_at: '2023-01-05T00:00:00Z' },
      { type: 'custom_stage_remove', name: 'A', created_at: '2023-01-06T00:00:00Z' },
      { type: 'custom_stage_add', name: 'B', created_at: '2023-01-07T00:00:00Z' } // B重新添加
    ];
    
    const result = calculateActiveCustomStages(log);
    expect(result).toEqual(['C', 'D', 'B']);
  });
});
