/**
 * 阶段过滤纯函数单元测试
 * 测试阶段过滤逻辑的正确性
 */

type ApplicationStage = '全部' | '申请' | '笔试' | '面试' | '谈判' | '录用' | '拒绝';

interface Job {
  id: string;
  company_name: string;
  position_title: string;
  location: string | null;
  work_location: 'remote' | 'in_person' | 'hybrid' | null;
  employment_type: 'full_time' | 'part_time' | 'co_op' | 'internship' | null;
  salary_range: string | null;
  created_at: string;
  keywords: string[] | null;
  application_status: string | null;
}

// 阶段过滤纯函数
export function filterJobsByStage(jobsList: Job[], stage: ApplicationStage): Job[] {
  if (stage === '全部') {
    return jobsList;
  }
  return jobsList.filter(job => job.application_status === stage);
}

describe('阶段过滤功能', () => {
  const mockJobs: Job[] = [
    {
      id: '1',
      company_name: 'Company A',
      position_title: 'Position 1',
      location: 'Vancouver',
      work_location: 'remote',
      employment_type: 'full_time',
      salary_range: '80K-100K',
      created_at: '2023-01-01T00:00:00Z',
      keywords: ['React', 'JavaScript'],
      application_status: '申请'
    },
    {
      id: '2',
      company_name: 'Company B',
      position_title: 'Position 2',
      location: 'Toronto',
      work_location: 'in_person',
      employment_type: 'full_time',
      salary_range: '90K-110K',
      created_at: '2023-01-02T00:00:00Z',
      keywords: ['Vue', 'TypeScript'],
      application_status: '面试'
    },
    {
      id: '3',
      company_name: 'Company C',
      position_title: 'Position 3',
      location: 'Montreal',
      work_location: 'hybrid',
      employment_type: 'part_time',
      salary_range: null,
      created_at: '2023-01-03T00:00:00Z',
      keywords: ['Python', 'Django'],
      application_status: '录用'
    },
    {
      id: '4',
      company_name: 'Company D',
      position_title: 'Position 4',
      location: 'Calgary',
      work_location: 'remote',
      employment_type: 'internship',
      salary_range: '50K-60K',
      created_at: '2023-01-04T00:00:00Z',
      keywords: ['Node.js'],
      application_status: null // 无状态，默认为申请阶段
    }
  ];

  test('选择"全部"应返回所有岗位', () => {
    const result = filterJobsByStage(mockJobs, '全部');
    expect(result).toHaveLength(4);
    expect(result).toEqual(mockJobs);
  });

  test('选择"申请"应只返回申请阶段的岗位', () => {
    const result = filterJobsByStage(mockJobs, '申请');
    expect(result).toHaveLength(1);
    expect(result[0].id).toBe('1');
    expect(result[0].application_status).toBe('申请');
  });

  test('选择"面试"应只返回面试阶段的岗位', () => {
    const result = filterJobsByStage(mockJobs, '面试');
    expect(result).toHaveLength(1);
    expect(result[0].id).toBe('2');
    expect(result[0].application_status).toBe('面试');
  });

  test('选择"录用"应只返回录用阶段的岗位', () => {
    const result = filterJobsByStage(mockJobs, '录用');
    expect(result).toHaveLength(1);
    expect(result[0].id).toBe('3');
    expect(result[0].application_status).toBe('录用');
  });

  test('选择"笔试"在无匹配岗位时应返回空数组', () => {
    const result = filterJobsByStage(mockJobs, '笔试');
    expect(result).toHaveLength(0);
    expect(result).toEqual([]);
  });

  test('空岗位列表应返回空数组', () => {
    const result = filterJobsByStage([], '申请');
    expect(result).toHaveLength(0);
    expect(result).toEqual([]);
  });

  test('所有阶段类型都应该正确过滤', () => {
    const stages: ApplicationStage[] = ['申请', '笔试', '面试', '谈判', '录用', '拒绝'];
    
    stages.forEach(stage => {
      const result = filterJobsByStage(mockJobs, stage);
      // 检查结果中的每个岗位都有正确的阶段
      result.forEach(job => {
        expect(job.application_status).toBe(stage);
      });
    });
  });
});
