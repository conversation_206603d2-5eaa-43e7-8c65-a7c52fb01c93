import { getActiveNotesFromLog } from '../notes-tab';

// 测试数据
const mockApplicationLog = [
  {
    type: 'status_changed' as const,
    from: null,
    to: '申请',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    type: 'note_add' as const,
    id: 'note-1',
    note: '准备申请材料',
    created_at: '2024-01-16T09:00:00Z'
  },
  {
    type: 'note_add' as const,
    id: 'note-2',
    note: '收到笔试邀请',
    created_at: '2024-01-18T14:30:00Z'
  },
  {
    type: 'custom_stage_add' as const,
    name: '初步筛选',
    created_at: '2024-01-17T11:00:00Z'
  },
  {
    type: 'note_add' as const,
    id: 'note-3',
    note: '完成技术面试',
    created_at: '2024-01-20T16:00:00Z'
  },
  {
    type: 'note_remove' as const,
    id: 'note-1',
    created_at: '2024-01-19T10:00:00Z'
  },
  {
    type: 'note_add' as const,
    id: 'note-4',
    note: '等待最终结果',
    created_at: '2024-01-22T09:00:00Z'
  }
];

describe('getActiveNotesFromLog', () => {
  describe('基本功能', () => {
    it('应该正确提取有效的笔记事件', () => {
      const result = getActiveNotesFromLog(mockApplicationLog);
      
      // 应该包含3条有效笔记（note-1被删除了）
      expect(result).toHaveLength(3);
      
      // 验证返回的笔记ID
      const noteIds = result.map(note => note.id);
      expect(noteIds).toContain('note-2');
      expect(noteIds).toContain('note-3');
      expect(noteIds).toContain('note-4');
      expect(noteIds).not.toContain('note-1'); // 已被删除
    });

    it('应该按时间倒序返回笔记（最新的在前）', () => {
      const result = getActiveNotesFromLog(mockApplicationLog);
      
      // 验证时间排序（从新到旧）
      expect(result[0].id).toBe('note-4'); // 2024-01-22
      expect(result[1].id).toBe('note-3'); // 2024-01-20
      expect(result[2].id).toBe('note-2'); // 2024-01-18
    });

    it('应该保留完整的笔记内容', () => {
      const result = getActiveNotesFromLog(mockApplicationLog);
      
      const note2 = result.find(note => note.id === 'note-2');
      expect(note2).toEqual({
        type: 'note_add',
        id: 'note-2',
        note: '收到笔试邀请',
        created_at: '2024-01-18T14:30:00Z'
      });
      
      const note4 = result.find(note => note.id === 'note-4');
      expect(note4).toEqual({
        type: 'note_add',
        id: 'note-4',
        note: '等待最终结果',
        created_at: '2024-01-22T09:00:00Z'
      });
    });

    it('应该忽略非笔记类型的事件', () => {
      const result = getActiveNotesFromLog(mockApplicationLog);
      
      // 结果中不应该包含 status_changed 或 custom_stage_add 事件
      const eventTypes = result.map(note => note.type);
      expect(eventTypes).toEqual(['note_add', 'note_add', 'note_add']);
    });
  });

  describe('软删除逻辑', () => {
    it('应该正确处理笔记的添加和删除序列', () => {
      const logWithMultipleOperations = [
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: '第一条笔记',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'note_add' as const,
          id: 'note-2',
          note: '第二条笔记',
          created_at: '2024-01-16T10:00:00Z'
        },
        {
          type: 'note_remove' as const,
          id: 'note-1',
          created_at: '2024-01-17T10:00:00Z'
        },
        {
          type: 'note_add' as const,
          id: 'note-3',
          note: '第三条笔记',
          created_at: '2024-01-18T10:00:00Z'
        },
        {
          type: 'note_remove' as const,
          id: 'note-2',
          created_at: '2024-01-19T10:00:00Z'
        }
      ];

      const result = getActiveNotesFromLog(logWithMultipleOperations);
      
      // 只有 note-3 应该存在
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('note-3');
      expect(result[0].note).toBe('第三条笔记');
    });

    it('应该正确处理同一笔记的重新添加', () => {
      const logWithReAdd = [
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: '原始笔记',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'note_remove' as const,
          id: 'note-1',
          created_at: '2024-01-16T10:00:00Z'
        },
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: '更新后的笔记',
          created_at: '2024-01-17T10:00:00Z'
        }
      ];

      const result = getActiveNotesFromLog(logWithReAdd);
      
      // 应该包含重新添加的笔记
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('note-1');
      expect(result[0].note).toBe('更新后的笔记');
      expect(result[0].created_at).toBe('2024-01-17T10:00:00Z');
    });

    it('应该处理删除不存在的笔记', () => {
      const logWithInvalidRemove = [
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: '存在的笔记',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'note_remove' as const,
          id: 'note-2', // 删除不存在的笔记
          created_at: '2024-01-16T10:00:00Z'
        }
      ];

      const result = getActiveNotesFromLog(logWithInvalidRemove);
      
      // note-1 应该仍然存在
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('note-1');
    });
  });

  describe('边界情况', () => {
    it('应该处理空日志', () => {
      const result = getActiveNotesFromLog([]);
      expect(result).toEqual([]);
    });

    it('应该处理只有非笔记事件的日志', () => {
      const nonNoteLog = [
        {
          type: 'status_changed' as const,
          from: null,
          to: '申请',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'custom_stage_add' as const,
          name: '筛选阶段',
          created_at: '2024-01-16T10:00:00Z'
        }
      ];

      const result = getActiveNotesFromLog(nonNoteLog);
      expect(result).toEqual([]);
    });

    it('应该处理只有删除事件的日志', () => {
      const onlyRemoveLog = [
        {
          type: 'note_remove' as const,
          id: 'note-1',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'note_remove' as const,
          id: 'note-2',
          created_at: '2024-01-16T10:00:00Z'
        }
      ];

      const result = getActiveNotesFromLog(onlyRemoveLog);
      expect(result).toEqual([]);
    });

    it('应该处理缺失必要字段的事件', () => {
      const incompleteLog = [
        {
          type: 'note_add' as const,
          // 缺少 id
          note: '没有ID的笔记',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'note_add' as const,
          id: 'note-1',
          // 缺少 note
          created_at: '2024-01-16T10:00:00Z'
        },
        {
          type: 'note_add' as const,
          id: 'note-2',
          note: '完整的笔记',
          created_at: '2024-01-17T10:00:00Z'
        }
      ];

      const result = getActiveNotesFromLog(incompleteLog);
      
      // 只有完整的笔记应该被包含
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('note-2');
      expect(result[0].note).toBe('完整的笔记');
    });

    it('应该处理无效的时间戳', () => {
      const invalidTimeLog = [
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: '无效时间的笔记',
          created_at: 'invalid-date'
        },
        {
          type: 'note_add' as const,
          id: 'note-2',
          note: '有效时间的笔记',
          created_at: '2024-01-15T10:00:00Z'
        }
      ];

      // 应该不会抛出错误
      expect(() => {
        const result = getActiveNotesFromLog(invalidTimeLog);
        expect(result).toHaveLength(2);
      }).not.toThrow();
    });
  });

  describe('数据完整性', () => {
    it('不应该修改原始日志数组', () => {
      const originalLog = [...mockApplicationLog];
      const result = getActiveNotesFromLog(mockApplicationLog);
      
      // 原始数组应该保持不变
      expect(mockApplicationLog).toEqual(originalLog);
      
      // 结果应该是新数组
      expect(result).not.toBe(mockApplicationLog);
    });

    it('应该正确处理时间顺序与操作逻辑', () => {
      // 测试乱序的日志事件
      const unorderedLog = [
        {
          type: 'note_remove' as const,
          id: 'note-1',
          created_at: '2024-01-17T10:00:00Z' // 删除操作在后
        },
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: '第一条笔记',
          created_at: '2024-01-15T10:00:00Z' // 添加操作在前
        },
        {
          type: 'note_add' as const,
          id: 'note-2',
          note: '第二条笔记',
          created_at: '2024-01-16T10:00:00Z'
        }
      ];

      const result = getActiveNotesFromLog(unorderedLog);
      
      // note-1 应该被删除，只有 note-2 存在
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('note-2');
    });
  });
});
