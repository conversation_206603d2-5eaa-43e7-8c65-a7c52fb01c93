import { sortEventsByTime } from '../timeline';

// 测试数据
const mockEvents = [
  {
    type: 'status_changed' as const,
    from: null,
    to: '申请',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    type: 'note_add' as const,
    id: 'note-1',
    note: '准备申请材料',
    created_at: '2024-01-16T09:00:00Z'
  },
  {
    type: 'custom_stage_add' as const,
    name: '初步筛选',
    created_at: '2024-01-17T14:30:00Z'
  },
  {
    type: 'status_changed' as const,
    from: '申请',
    to: '笔试',
    created_at: '2024-01-18T11:00:00Z'
  },
  {
    type: 'note_add' as const,
    id: 'note-2',
    note: '收到笔试邀请',
    created_at: '2024-01-19T16:00:00Z'
  },
  {
    type: 'custom_stage_remove' as const,
    name: '初步筛选',
    created_at: '2024-01-20T10:00:00Z'
  }
];

describe('sortEventsByTime', () => {
  describe('事件排序', () => {
    it('应该按时间倒序排序（最新的在前）', () => {
      const result = sortEventsByTime(mockEvents);
      
      // 验证排序顺序（从新到旧）
      expect(result[0].created_at).toBe('2024-01-20T10:00:00Z'); // custom_stage_remove
      expect(result[1].created_at).toBe('2024-01-19T16:00:00Z'); // note_add
      expect(result[2].created_at).toBe('2024-01-18T11:00:00Z'); // status_changed
      expect(result[3].created_at).toBe('2024-01-17T14:30:00Z'); // custom_stage_add
      expect(result[4].created_at).toBe('2024-01-16T09:00:00Z'); // note_add
      expect(result[5].created_at).toBe('2024-01-15T10:00:00Z'); // status_changed
    });

    it('应该处理相同时间的事件', () => {
      const eventsWithSameTime = [
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: 'Note 1',
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'status_changed' as const,
          from: null,
          to: '申请',
          created_at: '2024-01-15T10:00:00Z'
        }
      ];

      const result = sortEventsByTime(eventsWithSameTime);
      expect(result).toHaveLength(2);
      // 相同时间的事件应该都被包含，顺序可能会保持原有数组的相对顺序
    });
  });

  describe('边界情况', () => {
    it('应该处理空数组', () => {
      const result = sortEventsByTime([]);
      expect(result).toEqual([]);
    });

    it('应该处理缺少时间戳的事件', () => {
      const eventsWithBadTime = [
        {
          type: 'status_changed' as const,
          from: null,
          to: '申请',
          created_at: ''
        },
        {
          type: 'note_add' as const,
          id: 'note-1',
          note: 'Note',
          created_at: '2024-01-15T10:00:00Z'
        }
      ];

      const result = sortEventsByTime(eventsWithBadTime);
      expect(result).toHaveLength(2);
      // 应该能处理无效日期，不会抛出错误
    });

    it('应该处理不完整的事件对象', () => {
      const incompleteEvents = [
        {
          type: 'note_add' as const,
          // 缺少 id 和 note
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          type: 'custom_stage_add' as const,
          // 缺少 name
          created_at: '2024-01-16T10:00:00Z'
        }
      ];

      // 应该不会抛出错误，正常处理
      expect(() => {
        const result = sortEventsByTime(incompleteEvents);
        expect(result).toHaveLength(2);
      }).not.toThrow();
    });
  });

  describe('数据完整性', () => {
    it('不应该修改原始数组', () => {
      const originalEvents = [...mockEvents];
      const result = sortEventsByTime(mockEvents);
      
      // 原始数组应该保持不变
      expect(mockEvents).toEqual(originalEvents);
      
      // 结果应该是新数组
      expect(result).not.toBe(mockEvents);
    });

    it('应该保持事件对象的引用不变（浅拷贝）', () => {
      const result = sortEventsByTime(mockEvents);
      
      // 事件对象本身应该是同一个引用
      const originalFirstEvent = mockEvents.find(e => e.created_at === result[0].created_at);
      expect(result[0]).toBe(originalFirstEvent);
    });
  });
});
