'use client';

import { useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Clock, Edit3, FileText, Trash2, Plus, Minus, Check, X } from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

// 事件类型定义
interface TimelineEvent {
  type: 'status_changed' | 'custom_stage_add' | 'custom_stage_remove' | 'note_add' | 'note_remove';
  created_at: string;
  // status_changed 特有字段
  from?: string | null;
  to?: string;
  // custom_stage 特有字段
  name?: string;
  // note 特有字段
  id?: string;
  note?: string;
}

// 事件匹配器类型（用于定位事件）
interface EventMatcher {
  type: TimelineEvent['type'];
  id?: string;
  name?: string;
  from?: string | null;
  to?: string;
  created_at?: string;
}

interface TimelineProps {
  applicationLog: TimelineEvent[];
  onEventTimeUpdate?: (matcher: EventMatcher, newTime: string) => Promise<void>;
}

export function Timeline({ applicationLog, onEventTimeUpdate }: TimelineProps) {
  const [editingEvent, setEditingEvent] = useState<string | null>(null);
  const [tempDateTime, setTempDateTime] = useState<Date>(new Date());
  const [isUpdating, setIsUpdating] = useState(false);

  // 只进行排序，不进行过滤
  const sortedEvents = useMemo(() => {
    return sortEventsByTime(applicationLog);
  }, [applicationLog]);

  // 格式化日期时间显示
  const formatDateTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MM月dd日 HH:mm', { locale: zhCN });
    } catch {
      return dateString;
    }
  };

  // 获取事件图标
  const getEventIcon = (event: TimelineEvent) => {
    switch (event.type) {
      case 'status_changed':
        return <Calendar className="w-4 h-4 text-blue-500" />;
      case 'custom_stage_add':
        return <Plus className="w-4 h-4 text-green-500" />;
      case 'custom_stage_remove':
        return <Minus className="w-4 h-4 text-red-500" />;
      case 'note_add':
        return <FileText className="w-4 h-4 text-blue-500" />;
      case 'note_remove':
        return <Trash2 className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  // 渲染事件内容
  const renderEventContent = (event: TimelineEvent) => {
    switch (event.type) {
      case 'status_changed':
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium text-black">
              阶段变更：{event.from || '无'} → {event.to}
            </div>
            <div className="flex gap-2">
              {event.from && (
                <Badge variant="outline" className="text-xs text-gray-600 border-gray-300">
                  {event.from}
                </Badge>
              )}
              <span className="text-xs text-gray-500">→</span>
              <Badge variant="outline" className="text-xs text-gray-600 border-gray-300">
                {event.to}
              </Badge>
            </div>
          </div>
        );

      case 'custom_stage_add':
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium text-black">
              添加自定义阶段
            </div>
            <Badge variant="outline" className="text-xs text-gray-600 border-gray-300">
              {event.name}
            </Badge>
          </div>
        );

      case 'custom_stage_remove':
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium text-black">
              删除自定义阶段
            </div>
            <Badge variant="outline" className="text-xs text-gray-600 border-gray-300">
              {event.name}
            </Badge>
          </div>
        );

      case 'note_add':
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium text-black">
              添加笔记
            </div>
            {event.note && (
              <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded border-l-2 border-gray-300">
                {event.note}
              </div>
            )}
          </div>
        );

      case 'note_remove':
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium text-black">
              删除笔记
            </div>
          </div>
        );

      default:
        return (
          <div className="text-sm text-gray-600">
            未知事件类型
          </div>
        );
    }
  };

  // 生成事件匹配器（用于时间更新）
  const generateEventMatcher = (event: TimelineEvent): EventMatcher => {
    const matcher: EventMatcher = {
      type: event.type,
      created_at: event.created_at
    };

    switch (event.type) {
      case 'status_changed':
        matcher.from = event.from;
        matcher.to = event.to;
        break;
      case 'custom_stage_add':
      case 'custom_stage_remove':
        matcher.name = event.name;
        break;
      case 'note_add':
      case 'note_remove':
        matcher.id = event.id;
        break;
    }

    return matcher;
  };

  /**
   * 开始编辑时间轴事件的时间
   * @private
   * @param {TimelineEvent} event - 要编辑时间的事件对象
   * @description 设置编辑状态并初始化临时时间为事件当前时间
   */
  const handleStartEditTime = (event: TimelineEvent) => {
    setEditingEvent(`${event.type}-${event.created_at}`);
    setTempDateTime(new Date(event.created_at));
  };

  /**
   * 确认时间更新并提交到服务器
   * @private
   * @param {TimelineEvent} event - 要更新时间的事件对象
   * @returns {Promise<void>}
   * @description 调用父组件的更新回调，处理成功/失败状态，并重置编辑状态
   */
  const handleConfirmTimeUpdate = async (event: TimelineEvent) => {
    if (!onEventTimeUpdate) return;

    try {
      setIsUpdating(true);
      const matcher = generateEventMatcher(event);
      const newTimeString = tempDateTime.toISOString();
      await onEventTimeUpdate(matcher, newTimeString);
      setEditingEvent(null);
    } catch (error) {
      console.error('Failed to update event time:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  /**
   * 取消时间编辑操作
   * @private
   * @description 重置编辑状态和临时时间，回到正常显示模式
   */
  const handleCancelEdit = () => {
    setEditingEvent(null);
    setTempDateTime(new Date());
  };

  // 生成日期选择器选项（使用useMemo优化性能）
  const yearOptions = useMemo(() => {
    const currentYear = new Date().getFullYear();
    return Array.from({ length: 7 }, (_, i) => currentYear - 3 + i);
  }, []);

  const monthOptions = useMemo(() => 
    Array.from({ length: 12 }, (_, i) => i + 1)
  , []);

  const hourOptions = useMemo(() => 
    Array.from({ length: 24 }, (_, i) => i)
  , []);

  const minuteOptions = useMemo(() => 
    Array.from({ length: 60 }, (_, i) => i)
  , []);

  const dayOptions = useMemo(() => {
    const getDaysInMonth = (year: number, month: number) => new Date(year, month, 0).getDate();
    return Array.from(
      { length: getDaysInMonth(tempDateTime.getFullYear(), tempDateTime.getMonth() + 1) }, 
      (_, i) => i + 1
    );
  }, [tempDateTime]);

  return (
    <div className="space-y-4">
      {/* 时间轴内容 */}
      <div className="space-y-4">
        {sortedEvents.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <div>暂无事件记录</div>
          </div>
        ) : (
          sortedEvents.map((event, index) => (
            <div key={`${event.type}-${event.created_at}-${index}`} className="flex gap-4 group">
              {/* 时间轴线与图标 */}
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 rounded-full bg-white border-2 border-gray-200 flex items-center justify-center relative z-10">
                  {getEventIcon(event)}
                </div>
                {index < sortedEvents.length - 1 && (
                  <div className="w-px h-8 bg-gray-200 mt-2" />
                )}
              </div>

              {/* 事件内容 */}
              <div className="flex-1 pb-8">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    {renderEventContent(event)}
                  </div>
                  
                  {/* 时间与操作 */}
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    {editingEvent !== `${event.type}-${event.created_at}` && (
                      <>
                        <span>{formatDateTime(event.created_at)}</span>
                        {onEventTimeUpdate && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleStartEditTime(event)}
                            className="h-5 w-5 p-0 text-gray-400 hover:text-gray-600"
                          >
                            <Edit3 className="h-3 w-3" />
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </div>
                
                {/* 时间编辑器 - 显示在事件内容下方 */}
                {editingEvent === `${event.type}-${event.created_at}` && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
                    <div className="space-y-4">
                      <div className="text-center">
                        <span className="text-sm font-medium text-black">编辑时间</span>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-3">
                        {/* 年份 */}
                        <div className="space-y-2">
                          <span className="text-xs text-gray-600">年</span>
                          <Select 
                            value={tempDateTime.getFullYear().toString()} 
                            onValueChange={(value) => {
                              const newDate = new Date(tempDateTime);
                              newDate.setFullYear(parseInt(value));
                              setTempDateTime(newDate);
                            }}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {yearOptions.map((year) => (
                                <SelectItem key={year} value={year.toString()}>
                                  {year}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        {/* 月份 */}
                        <div className="space-y-2">
                          <span className="text-xs text-gray-600">月</span>
                          <Select 
                            value={(tempDateTime.getMonth() + 1).toString()}
                            onValueChange={(value) => {
                              const newDate = new Date(tempDateTime);
                              newDate.setMonth(parseInt(value) - 1);
                              setTempDateTime(newDate);
                            }}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {monthOptions.map((month) => (
                                <SelectItem key={month} value={month.toString()}>
                                  {month}月
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        {/* 日期 */}
                        <div className="space-y-2">
                          <span className="text-xs text-gray-600">日</span>
                          <Select 
                            value={tempDateTime.getDate().toString()}
                            onValueChange={(value) => {
                              const newDate = new Date(tempDateTime);
                              newDate.setDate(parseInt(value));
                              setTempDateTime(newDate);
                            }}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {dayOptions.map((day) => (
                                <SelectItem key={day} value={day.toString()}>
                                  {day}日
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        {/* 小时 */}
                        <div className="space-y-2">
                          <span className="text-xs text-gray-600">时</span>
                          <Select 
                            value={tempDateTime.getHours().toString()}
                            onValueChange={(value) => {
                              const newDate = new Date(tempDateTime);
                              newDate.setHours(parseInt(value));
                              setTempDateTime(newDate);
                            }}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {hourOptions.map((hour) => (
                                <SelectItem key={hour} value={hour.toString()}>
                                  {hour.toString().padStart(2, '0')}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        {/* 分钟 */}
                        <div className="space-y-2 col-span-2">
                          <span className="text-xs text-gray-600">分</span>
                          <Select 
                            value={tempDateTime.getMinutes().toString()}
                            onValueChange={(value) => {
                              const newDate = new Date(tempDateTime);
                              newDate.setMinutes(parseInt(value));
                              setTempDateTime(newDate);
                            }}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {minuteOptions.map((minute) => (
                                <SelectItem key={minute} value={minute.toString()}>
                                  {minute.toString().padStart(2, '0')}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCancelEdit}
                          disabled={isUpdating}
                          className="flex-1"
                        >
                          <X className="w-4 h-4 mr-2" />
                          取消
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleConfirmTimeUpdate(event)}
                          disabled={isUpdating}
                          className="flex-1"
                        >
                          {isUpdating ? (
                            <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                          ) : (
                            <Check className="w-4 h-4 mr-2" />
                          )}
                          确认
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

// 纯函数：事件按时间排序
export function sortEventsByTime(events: TimelineEvent[]): TimelineEvent[] {
  // 按时间倒序排序（最新的在上面）
  return [...events].sort((a, b) => {
    const dateA = new Date(a.created_at).getTime();
    const dateB = new Date(b.created_at).getTime();
    return dateB - dateA; // 倒序
  });
}