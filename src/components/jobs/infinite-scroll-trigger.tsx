'use client';

import { useEffect, useRef } from 'react';

interface InfiniteScrollTriggerProps {
  onLoadMore: () => void;
  isLoading: boolean;
  hasMore: boolean;
  threshold?: number; // Distance from bottom to trigger load (in pixels)
  error?: string | null;
  onRetry?: () => void;
}

export function InfiniteScrollTrigger({
  onLoadMore,
  isLoading,
  hasMore,
  threshold = 200
}: InfiniteScrollTriggerProps) {
  const triggerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const trigger = triggerRef.current;
    if (!trigger || !hasMore || isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !isLoading) {
          onLoadMore();
        }
      },
      {
        rootMargin: `${threshold}px`,
        threshold: 0.1
      }
    );

    observer.observe(trigger);

    return () => {
      observer.unobserve(trigger);
    };
  }, [onLoadMore, isLoading, hasMore, threshold]);

  if (!hasMore) {
    return null;
  }

  return (
    <div 
      ref={triggerRef}
      className="flex justify-center py-8"
    >
      {isLoading && (
        <div className="flex items-center gap-2 text-gray-500">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
          <span>加载更多职位...</span>
        </div>
      )}
    </div>
  );
}
