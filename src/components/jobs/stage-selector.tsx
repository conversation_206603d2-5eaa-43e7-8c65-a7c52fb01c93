'use client';

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, Plus, Trash2, Edit2, FileText, PenTool, Users, Handshake, CheckCircle, XCircle, Settings, Star } from "lucide-react";
import { cn } from "@/lib/utils";

// 系统阶段类型
type SystemStage = '申请' | '笔试' | '面试' | '谈判' | '录用' | '拒绝';

// 应用日志事件类型
interface ApplicationEvent {
  type: 'status_changed' | 'custom_stage_add' | 'custom_stage_remove' | 'note_add' | 'note_remove';
  from?: string;
  to?: string;
  name?: string;
  id?: string;
  note?: string;
  created_at: string;
}

interface StageSelectorProps {
  currentStage: SystemStage | null;
  applicationLog: ApplicationEvent[];
  onStageChange: (stage: SystemStage) => Promise<void>;
  onCustomStageAdd: (name: string) => Promise<void>;
  onCustomStageRemove: (name: string) => Promise<void>;
  disabled?: boolean;
  className?: string;
}

// 计算当前活跃的自定义阶段集合
export function calculateActiveCustomStages(log: ApplicationEvent[]): string[] {
  const stageMap = new Map<string, boolean>();
  
  log.forEach(event => {
    if (event.type === 'custom_stage_add' && event.name) {
      stageMap.set(event.name, true);
    } else if (event.type === 'custom_stage_remove' && event.name) {
      stageMap.set(event.name, false);
    }
  });
  
  return Array.from(stageMap.entries())
    .filter(([, isActive]) => isActive)
    .map(([name]) => name);
}

// 获取阶段对应的图标
function getStageIcon(stage: string | null) {
  const systemStages = ['申请', '笔试', '面试', '谈判', '录用', '拒绝'];
  
  switch (stage) {
    case '申请':
      return <FileText className="w-4 h-4 text-blue-500" />;
    case '笔试':
      return <PenTool className="w-4 h-4 text-yellow-500" />;
    case '面试':
      return <Users className="w-4 h-4 text-orange-500" />;
    case '谈判':
      return <Handshake className="w-4 h-4 text-purple-500" />;
    case '录用':
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    case '拒绝':
      return <XCircle className="w-4 h-4 text-red-500" />;
    default:
      // 如果不是系统阶段，就是自定义阶段，使用红色星星图标
      if (stage && !systemStages.includes(stage)) {
        return <Star className="w-4 h-4 text-red-500" />;
      }
      return <Settings className="w-4 h-4 text-gray-500" />;
  }
}

// 获取当前实际阶段（从最新的状态变更事件中获取）
export function getCurrentActualStage(log: ApplicationEvent[], fallbackStage: string | null): string | null {
  // 按时间倒序查找最新的状态变更事件
  const sortedLog = [...log].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  
  for (const event of sortedLog) {
    if (event.type === 'status_changed' && event.to) {
      return event.to;
    }
  }
  
  // 如果没有找到状态变更事件，返回fallback值
  return fallbackStage;
}

export function StageSelector({
  currentStage,
  applicationLog,
  onStageChange,
  onCustomStageAdd,
  onCustomStageRemove,
  disabled = false,
  className
}: StageSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [newCustomStage, setNewCustomStage] = useState('');
  const [isAddingStage, setIsAddingStage] = useState(false);
  const [deletingStages, setDeletingStages] = useState<Set<string>>(new Set());
  const [isChangingStage, setIsChangingStage] = useState(false);


  const systemStages: SystemStage[] = ['申请', '笔试', '面试', '谈判', '录用', '拒绝'];
  const activeCustomStages = calculateActiveCustomStages(applicationLog);

  const handleSystemStageChange = async (stage: SystemStage) => {
    try {
      setIsChangingStage(true);
      // 确保加载状态至少显示300ms，让用户能看到
      const [result] = await Promise.all([
        onStageChange(stage),
        new Promise(resolve => setTimeout(resolve, 300))
      ]);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to change stage:', error);
    } finally {
      setIsChangingStage(false);
    }
  };

  const handleAddCustomStage = async () => {
    const stageName = newCustomStage.trim();
    if (!stageName || activeCustomStages.includes(stageName)) return;

    try {
      setIsAddingStage(true);
      await onCustomStageAdd(stageName);
      
      // 添加成功后自动选中这个自定义阶段作为当前阶段
      onStageChange(stageName as SystemStage);
      setNewCustomStage('');
      setIsOpen(false); // 关闭选择器
    } catch (error) {
      console.error('Failed to add custom stage:', error);
    } finally {
      setIsAddingStage(false);
    }
  };

  const handleRemoveCustomStage = async (name: string) => {
    try {
      setDeletingStages(prev => new Set(prev.add(name)));
      await onCustomStageRemove(name);
    } catch (error) {
      console.error('Failed to remove custom stage:', error);
    } finally {
      setDeletingStages(prev => {
        const newSet = new Set(prev);
        newSet.delete(name);
        return newSet;
      });
    }
  };





  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "justify-between group",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          disabled={disabled || isChangingStage}
        >
          <span className="flex items-center gap-2">
            {isChangingStage ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            ) : (
              getStageIcon(currentStage)
            )}
            {currentStage || '申请'}
          </span>
          <ChevronDown className="h-4 w-4 opacity-0 group-hover:opacity-50 transition-opacity" />
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80" align="start">
        <div className="space-y-4">
          {/* 所有阶段选择（系统 + 自定义） */}
          <div className="space-y-3">
            <h4 className="font-medium leading-none">选择阶段</h4>
            <RadioGroup
              value={currentStage || ''}
              onValueChange={(value) => {
                handleSystemStageChange(value as SystemStage);
              }}
              className="space-y-2"
            >
              {/* 系统阶段 */}
              {systemStages.map((stage) => (
                <div key={stage} className="flex items-center space-x-2">
                  <RadioGroupItem value={stage} id={`stage-${stage}`} />
                  <Label
                    htmlFor={`stage-${stage}`}
                    className="text-sm cursor-pointer flex items-center gap-2"
                  >
                    {getStageIcon(stage)}
                    {stage}
                  </Label>
                </div>
              ))}
              
              {/* 自定义阶段 */}
              {activeCustomStages.map((stage) => (
                <div key={stage} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value={stage} id={`custom-stage-${stage}`} />
                    <Label
                      htmlFor={`custom-stage-${stage}`}
                      className="text-sm cursor-pointer flex items-center gap-2"
                    >
                      {getStageIcon(stage)}
                      {stage}
                    </Label>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleRemoveCustomStage(stage);
                    }}
                    disabled={deletingStages.has(stage)}
                    className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                  >
                    {deletingStages.has(stage) ? (
                      <div className="h-3 w-3 animate-spin rounded-full border border-current border-t-transparent" />
                    ) : (
                      <Trash2 className="h-3 w-3" />
                    )}
                  </Button>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* 添加新自定义阶段 */}
          <div className="space-y-3 border-t pt-4">
            <h4 className="font-medium leading-none">添加自定义阶段</h4>
            
            <div className="flex gap-2 items-center">
              <Input
                placeholder="输入自定义阶段名称"
                value={newCustomStage}
                onChange={(e) => setNewCustomStage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isAddingStage) {
                    e.preventDefault();
                    handleAddCustomStage();
                  }
                }}
                disabled={isAddingStage}
                className="text-sm h-8"
              />
              <Button
                size="sm"
                onClick={handleAddCustomStage}
                disabled={!newCustomStage.trim() || activeCustomStages.includes(newCustomStage.trim()) || isAddingStage}
                className="h-8 w-8 p-0 flex-shrink-0"
              >
                {isAddingStage ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>


        </div>
      </PopoverContent>
    </Popover>
  );
}
