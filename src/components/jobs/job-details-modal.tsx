'use client';

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { StageSelector, getCurrentActualStage } from "./stage-selector";
import { OverviewTab } from "./overview-tab";
import { ResumesTab } from "./resumes-tab";
import { NotesTab } from "./notes-tab";
import { FileText, User, StickyNote, BookOpen } from "lucide-react";
import type { Job, SystemStage, ApplicationLogEvent } from "@/lib/types";

interface JobDetailsModalProps {
  job: Job | null;
  isOpen: boolean;
  onClose: () => void;
  onStageChange?: (jobId: string, stage: SystemStage) => void;
  onCustomStageAdd?: (jobId: string, name: string) => void;
  onCustomStageRemove?: (jobId: string, name: string) => void;
  onEventTimeUpdate?: (jobId: string, matcher: Partial<ApplicationLogEvent>, newTime: string) => void;
  onNoteAdd?: (jobId: string, note: string, createdAt?: string) => void;
  onNoteRemove?: (jobId: string, noteId: string, createdAt?: string) => void;
  onJobFieldsUpdate?: (jobId: string, partialJob: any) => Promise<{ success: boolean; error?: string }>;
}

export function JobDetailsModal({
  job,
  isOpen,
  onClose,
  onStageChange,
  onCustomStageAdd,
  onCustomStageRemove,
  onEventTimeUpdate,
  onNoteAdd,
  onNoteRemove,
  onJobFieldsUpdate
}: JobDetailsModalProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 重置状态当job改变时（仅在job ID变化时重置，而不是job内容变化时）
  const [previousJobId, setPreviousJobId] = useState<string | null>(null);
  useEffect(() => {
    if (job && job.id !== previousJobId) {
      setActiveTab('overview');
      setHasUnsavedChanges(false);
      setPreviousJobId(job.id);
    } else if (!job) {
      setPreviousJobId(null);
    }
  }, [job, previousJobId]);

  // 关闭处理，检查未保存的更改
  const handleClose = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm('您有未保存的更改，确定要关闭吗？');
      if (!confirmed) return;
    }
    setHasUnsavedChanges(false);
    onClose();
  };

  // 主按钮逻辑 - 统一为生成定制简历
  const primaryButton = {
    text: '生成定制简历',
    action: () => {
      console.log('生成定制简历');
      // TODO: 实现生成简历逻辑
    }
  };

  // 处理添加笔记按钮
  const handleAddNote = () => {
    setActiveTab('notes');
    // TODO: 聚焦到笔记编辑器
  };

  if (!job) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent 
        className="max-w-4xl h-[90vh] overflow-hidden p-0 flex flex-col"
        onPointerDownOutside={(e) => {
          if (hasUnsavedChanges) {
            e.preventDefault();
          }
        }}
      >
        {/* 固定顶部区域 */}
        <DialogHeader className="px-6 py-4 border-b flex-shrink-0">
          <DialogTitle className="sr-only">
            {job.position_title} @ {job.company_name} - 岗位详情
          </DialogTitle>
          <div className="space-y-4">
            {/* 第一行：职位标题 @ 公司名称 */}
            <div className="flex items-center gap-3">
              <h2 className="text-xl font-semibold tracking-tight truncate">
                {job.position_title}
              </h2>
              <span className="text-muted-foreground">@</span>
              <span className="text-lg text-muted-foreground truncate">
                {job.company_name}
              </span>
            </div>

            {/* 第二行：操作按钮组（左对齐） */}
            <div className="flex items-center gap-2">
              {/* 状态选择器 */}
                              <StageSelector
                  currentStage={getCurrentActualStage(job.application_log || [], job.application_status) as any}
                  applicationLog={job.application_log || []}
                  onStageChange={async (stage) => {
                    if (onStageChange) {
                      await onStageChange(job.id, stage);
                    }
                  }}
                onCustomStageAdd={async (name) => {
                  if (onCustomStageAdd) {
                    await onCustomStageAdd(job.id, name);
                  }
                }}
                onCustomStageRemove={async (name) => {
                  if (onCustomStageRemove) {
                    await onCustomStageRemove(job.id, name);
                  }
                }}
                className="text-sm"
              />
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddNote}
              >
                <StickyNote className="h-4 w-4 mr-2" />
                添加笔记
              </Button>
              
              <Button
                size="sm"
                onClick={primaryButton.action}
              >
                <FileText className="h-4 w-4 mr-2" />
                {primaryButton.text}
              </Button>
            </div>


          </div>
        </DialogHeader>

        {/* Tabs 内容区域 */}
        <div className="flex-1 overflow-hidden min-h-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            {/* Tabs 标签栏 */}
            <TabsList className="grid grid-cols-4 w-full rounded-none border-b flex-shrink-0">
              <TabsTrigger 
                value="overview" 
                className="flex items-center gap-2"
              >
                <User className="h-4 w-4" />
                概览
              </TabsTrigger>
              <TabsTrigger 
                value="resumes"
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                简历
              </TabsTrigger>
              <TabsTrigger 
                value="notes"
                className="flex items-center gap-2"
              >
                <StickyNote className="h-4 w-4" />
                笔记
              </TabsTrigger>
              <TabsTrigger 
                value="guide"
                className="flex items-center gap-2"
              >
                <BookOpen className="h-4 w-4" />
                攻略
                <Badge variant="secondary" className="ml-1 text-xs">
                  即将上线
                </Badge>
              </TabsTrigger>
            </TabsList>

            {/* Tab 内容区域 - 可滚动 */}
            <div className="flex-1 overflow-hidden min-h-0">
              <TabsContent value="overview" className="m-0 h-full overflow-auto">
                <OverviewTab 
                  job={job}
                  onSave={onJobFieldsUpdate ? (updatedFields) => onJobFieldsUpdate(job.id, updatedFields) : undefined}
                  onEventTimeUpdate={onEventTimeUpdate ? (matcher, newTime) => onEventTimeUpdate(job.id, matcher, newTime) : undefined}
                />
              </TabsContent>

              <TabsContent value="resumes" className="m-0 h-full overflow-auto">
                <ResumesTab 
                  jobId={job.id}
                  onGenerateResume={() => {
                    console.log('生成定制简历 for job:', job.id);
                    // TODO: 实现生成定制简历逻辑
                  }}
                />
              </TabsContent>

              <TabsContent value="notes" className="m-0 h-full overflow-auto">
                <NotesTab 
                  applicationLog={job.application_log || []}
                  onNoteAdd={onNoteAdd ? (note, createdAt) => onNoteAdd(job.id, note, createdAt) : undefined}
                  onNoteRemove={onNoteRemove ? (noteId, createdAt) => onNoteRemove(job.id, noteId, createdAt) : undefined}
                />
              </TabsContent>

              <TabsContent value="guide" className="m-0 p-6">
                <div className="text-center py-12">
                  <div className="max-w-md mx-auto">
                    <BookOpen className="h-16 w-16 mx-auto mb-6 opacity-50" />
                    <h3 className="text-lg font-semibold tracking-tight mb-3">
                      面试攻略功能即将上线
                    </h3>
                    <p className="text-muted-foreground mb-6">
                      我们正在开发智能面试攻略功能，将为您提供：
                    </p>
                    <ul className="text-left text-sm text-muted-foreground space-y-2 mb-8">
                      <li>• 公司背景和文化分析</li>
                      <li>• 常见面试问题预测</li>
                      <li>• 个性化回答建议</li>
                      <li>• 技术问题准备清单</li>
                    </ul>
                    <Badge variant="secondary">
                      敬请期待
                    </Badge>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
