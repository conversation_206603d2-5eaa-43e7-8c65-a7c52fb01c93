'use client';

import { useC<PERSON>back, useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Briefcase, Building2, MapPin, Clock, DollarSign, Trash2} from "lucide-react";
import {
  getJobListingsInfinite,
  deleteJob,
  setSystemStage,
  addCustomStage,
  removeCustomStage,
  addNote,
  softDeleteNote,
  updateEventTime,
  updateJobFields
} from "@/utils/actions/jobs/actions";

import { toast } from "sonner";
import type { SystemStage, ApplicationLogEvent, EventMatcher } from "@/lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

import { StageSelector, getCurrentActualStage } from "./stage-selector";
import { JobDetailsModal } from "./job-details-modal";
import { InfiniteScrollTrigger } from "./infinite-scroll-trigger";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";


type WorkLocationType = 'remote' | 'in_person' | 'hybrid';
type EmploymentType = 'full_time' | 'part_time' | 'co_op' | 'internship';
type ApplicationStage = '全部' | '申请' | '笔试' | '面试' | '谈判' | '录用' | '拒绝';

// Use the main Job type from lib/types
import type { Job as MainJob } from "@/lib/types";
type Job = MainJob;

interface JobListingsCardProps {
  selectedStage?: ApplicationStage;
}

export function JobListingsCard({ selectedStage = '全部' }: JobListingsCardProps) {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [workLocation, setWorkLocation] = useState<WorkLocationType | undefined>();
  const [employmentType, setEmploymentType] = useState<EmploymentType | undefined>();
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [optimisticUpdates, setOptimisticUpdates] = useState<Map<string, Partial<Job>>>(new Map());

  // 乐观更新工具函数
  const applyOptimisticUpdate = useCallback((jobId: string, update: Partial<Job>) => {
    setOptimisticUpdates(prev => new Map(prev.set(jobId, { ...prev.get(jobId), ...update })));
  }, []);

  const clearOptimisticUpdate = useCallback((jobId: string) => {
    setOptimisticUpdates(prev => {
      const newMap = new Map(prev);
      newMap.delete(jobId);
      return newMap;
    });
  }, []);

  // 获取带乐观更新的job数据
  const getJobWithOptimisticUpdate = useCallback((job: Job): Job => {
    const update = optimisticUpdates.get(job.id);
    return update ? { ...job, ...update } : job;
  }, [optimisticUpdates]);

  // 创建获取函数用于无限滚动
  const fetchJobsInfinite = useCallback(async (cursor?: { created_at: string; id: string }) => {
    const result = await getJobListingsInfinite({
      pageSize: 6,
      cursor,
      filters: {
        workLocation,
        employmentType
      }
    });

    return {
      data: result.jobs,
      hasMore: result.hasMore,
      nextCursor: result.nextCursor
    };
  }, [workLocation, employmentType]);

  // 使用无限滚动hook
  const {
    data: allJobs,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    loadMore,
    refresh,
    retry,
    updateItem
  } = useInfiniteScroll({
    fetchFunction: fetchJobsInfinite,
    getItemId: (job: Job) => job.id
  });

  // 更新单个job的辅助函数 - 现在使用就地更新而不是刷新
  const updateJobInData = useCallback((jobId: string, updatedJob: Job) => {
    updateItem(jobId, updatedJob);
  }, [updateItem]);

  // 阶段过滤函数
  const filterJobsByStage = useCallback((jobsList: Job[], stage: ApplicationStage): Job[] => {
    if (stage === '全部') {
      return jobsList;
    }
    return jobsList.filter(job => {
      const updatedJob = getJobWithOptimisticUpdate(job);
      return updatedJob.application_status === stage;
    });
  }, [getJobWithOptimisticUpdate]);

  // 应用阶段过滤
  useEffect(() => {
    const filteredJobs = filterJobsByStage(allJobs, selectedStage);
    setJobs(filteredJobs);
  }, [allJobs, selectedStage, filterJobsByStage]);

  // 当过滤条件改变时重新加载
  useEffect(() => {
    refresh();
  }, [workLocation, employmentType, refresh]);



  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      'day'
    );
  };

  const formatWorkLocation = (workLocation: Job['work_location']) => {
    if (!workLocation) return '未指定';
    if (workLocation === 'remote') return '远程';
    if (workLocation === 'in_person') return '现场';
    if (workLocation === 'hybrid') return '混合';
    return String(workLocation).replace('_', ' ');
  }

  const handleDeleteJob = async (jobId: string) => {
    try {
      await deleteJob(jobId);
      // Refresh jobs after deletion
      refresh();
    } catch (error) {
      console.error('Error deleting job:', error);
    }
  };

  const handleJobClick = (job: Job) => {
    setSelectedJob(job);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedJob(null);
  };

  // 处理阶段变更的Server Action
  const handleStageChange = useCallback(async (jobId: string, stage: SystemStage) => {
    const job = allJobs.find(j => j.id === jobId);
    if (!job) return;

    const previousStage = job.application_status as SystemStage | null;
    
    // 创建乐观更新
    const statusEvent: ApplicationLogEvent = {
      type: 'status_changed',
      from: previousStage,
      to: stage,
      created_at: new Date().toISOString()
    };
    
    const optimisticUpdate = {
      application_status: stage,
      application_log: [...(job.application_log || []), statusEvent]
    };

    // 立即应用乐观更新
    applyOptimisticUpdate(jobId, optimisticUpdate);

    try {
      const result = await setSystemStage(jobId, previousStage, stage);
      
      if (result.success) {
        // 清除乐观更新，使用服务器返回的数据
        clearOptimisticUpdate(jobId);
        
        // 更新本地数据
        updateJobInData(jobId, result.job!);
        
        // 如果modal打开的是同一个job，也要更新
        if (selectedJob?.id === jobId) {
          setSelectedJob(result.job!);
        }
        
        toast.success("阶段更新成功", {
          description: `已将申请阶段更新为：${stage}`
        });
      } else {
        // 失败时回滚乐观更新
        clearOptimisticUpdate(jobId);
        toast.error("阶段更新失败", {
          description: result.error || "请稍后重试"
        });
      }
    } catch (error) {
      // 回滚乐观更新
      clearOptimisticUpdate(jobId);
      console.error('Stage change error:', error);
      toast.error("阶段更新失败", {
        description: "网络错误，请稍后重试"
      });
    }
  }, [allJobs, selectedJob, applyOptimisticUpdate, clearOptimisticUpdate]);

  // 处理自定义阶段添加
  const handleCustomStageAdd = useCallback(async (jobId: string, name: string): Promise<void> => {
    const job = allJobs.find(j => j.id === jobId);
    if (!job) return;

    // 创建乐观更新
    const stageEvent: ApplicationLogEvent = {
      type: 'custom_stage_add',
      name,
      created_at: new Date().toISOString()
    };
    
    const optimisticUpdate = {
      application_log: [...(job.application_log || []), stageEvent]
    };

    // 立即应用乐观更新
    applyOptimisticUpdate(jobId, optimisticUpdate);

    try {
      const result = await addCustomStage(jobId, name);
      
      if (result.success) {
        // 清除乐观更新，使用服务器返回的数据
        clearOptimisticUpdate(jobId);
        
        // 更新本地数据
        updateJobInData(jobId, result.job!);
        
        // 如果modal打开的是同一个job，也要更新
        if (selectedJob?.id === jobId) {
          setSelectedJob(result.job!);
        }
        
        toast.success("自定义阶段添加成功", {
          description: `已添加自定义阶段：${name}`
        });
      } else {
        // 失败时回滚乐观更新
        clearOptimisticUpdate(jobId);
        toast.error("添加自定义阶段失败", {
          description: result.error || "请稍后重试"
        });
      }
    } catch (error) {
      // 回滚乐观更新
      clearOptimisticUpdate(jobId);
      console.error('Custom stage add error:', error);
      toast.error("添加自定义阶段失败", {
        description: "网络错误，请稍后重试"
      });
    }
  }, [allJobs, selectedJob, applyOptimisticUpdate, clearOptimisticUpdate]);

  // 处理自定义阶段删除
  const handleCustomStageRemove = useCallback(async (jobId: string, name: string): Promise<void> => {
    const job = allJobs.find(j => j.id === jobId);
    if (!job) return;

    // 对于删除操作，我们直接调用服务器，不进行乐观更新
    // 因为删除逻辑比较复杂（需要移除相关事件）

    try {
      const result = await removeCustomStage(jobId, name);
      
      if (result.success) {
        // 更新本地数据
        updateJobInData(jobId, result.job!);
        
        // 如果modal打开的是同一个job，也要更新
        if (selectedJob?.id === jobId) {
          setSelectedJob(result.job!);
        }
        
        toast.success("自定义阶段删除成功", {
          description: `已删除自定义阶段：${name}`
        });
      } else {
        toast.error("删除自定义阶段失败", {
          description: result.error || "请稍后重试"
        });
      }
    } catch (error) {
      console.error('Custom stage remove error:', error);
      toast.error("删除自定义阶段失败", {
        description: "网络错误，请稍后重试"
      });
    }
  }, [allJobs, selectedJob]);

  // 处理笔记添加
  const handleNoteAdd = useCallback(async (jobId: string, note: string, createdAt?: string) => {
    const job = allJobs.find(j => j.id === jobId);
    if (!job) return;

    // 生成唯一ID
    const noteId = crypto.randomUUID();
    
    // 创建乐观更新
    const noteEvent: ApplicationLogEvent = {
      type: 'note_add',
      id: noteId,
      note,
      created_at: createdAt || new Date().toISOString()
    };
    
    const optimisticUpdate = {
      application_log: [...(job.application_log || []), noteEvent]
    };

    // 立即应用乐观更新
    applyOptimisticUpdate(jobId, optimisticUpdate);

    try {
      const result = await addNote(jobId, noteId, note, createdAt);
      
      if (result.success) {
        // 清除乐观更新，使用服务器返回的数据
        clearOptimisticUpdate(jobId);
        
        // 更新本地数据
        updateJobInData(jobId, result.job!);
        
        // 如果modal打开的是同一个job，也要更新
        if (selectedJob?.id === jobId) {
          setSelectedJob(result.job!);
        }
        
        toast.success("笔记添加成功", {
          description: "您的笔记已保存"
        });
      } else {
        // 失败时回滚乐观更新
        clearOptimisticUpdate(jobId);
        toast.error("笔记添加失败", {
          description: result.error || "请稍后重试"
        });
      }
    } catch (error) {
      // 回滚乐观更新
      clearOptimisticUpdate(jobId);
      console.error('Note add error:', error);
      toast.error("笔记添加失败", {
        description: "网络错误，请稍后重试"
      });
    }
  }, [allJobs, selectedJob, applyOptimisticUpdate, clearOptimisticUpdate]);

  // 处理笔记删除
  const handleNoteRemove = useCallback(async (jobId: string, noteId: string, createdAt?: string) => {
    const job = allJobs.find(j => j.id === jobId);
    if (!job) return;

    // 创建乐观更新
    const noteEvent: ApplicationLogEvent = {
      type: 'note_remove',
      id: noteId,
      created_at: createdAt || new Date().toISOString()
    };
    
    const optimisticUpdate = {
      application_log: [...(job.application_log || []), noteEvent]
    };

    // 立即应用乐观更新
    applyOptimisticUpdate(jobId, optimisticUpdate);

    try {
      const result = await softDeleteNote(jobId, noteId, createdAt);
      
      if (result.success) {
        // 清除乐观更新，使用服务器返回的数据
        clearOptimisticUpdate(jobId);
        
        // 更新本地数据
        updateJobInData(jobId, result.job!);
        
        // 如果modal打开的是同一个job，也要更新
        if (selectedJob?.id === jobId) {
          setSelectedJob(result.job!);
        }
        
        toast.success("笔记删除成功", {
          description: "笔记已删除"
        });
      } else {
        // 失败时回滚乐观更新
        clearOptimisticUpdate(jobId);
        toast.error("笔记删除失败", {
          description: result.error || "请稍后重试"
        });
      }
    } catch (error) {
      // 回滚乐观更新
      clearOptimisticUpdate(jobId);
      console.error('Note remove error:', error);
      toast.error("笔记删除失败", {
        description: "网络错误，请稍后重试"
      });
    }
  }, [allJobs, selectedJob, applyOptimisticUpdate, clearOptimisticUpdate]);

  // 处理事件时间更新
  const handleEventTimeUpdate = useCallback(async (jobId: string, matcher: Partial<ApplicationLogEvent>, newTime: string) => {
    const job = allJobs.find(j => j.id === jobId);
    if (!job) return;

    // 对于时间更新，我们不做乐观更新，直接调用服务器
    try {
      const result = await updateEventTime(jobId, matcher as EventMatcher, newTime);
      
      if (result.success) {
        // 更新本地数据
        updateJobInData(jobId, result.job!);
        
        // 如果modal打开的是同一个job，也要更新
        if (selectedJob?.id === jobId) {
          setSelectedJob(result.job!);
        }
        
        toast.success("事件时间更新成功", {
          description: "时间已更新"
        });
      } else {
        toast.error("事件时间更新失败", {
          description: result.error || "请稍后重试"
        });
      }
    } catch (error) {
      console.error('Event time update error:', error);
      toast.error("事件时间更新失败", {
        description: "网络错误，请稍后重试"
      });
    }
  }, [allJobs, selectedJob]);

  // 处理岗位字段更新 (用于Overview Tab)
  const handleJobFieldsUpdate = useCallback(async (jobId: string, partialJob: Parameters<typeof updateJobFields>[1]) => {
    const job = allJobs.find(j => j.id === jobId);
    if (!job) return { success: false, error: "Job not found" };

    try {
      const result = await updateJobFields(jobId, partialJob);
      
      if (result.success) {
        // 更新本地数据
        updateJobInData(jobId, result.job!);
        
        // 如果modal打开的是同一个job，也要更新
        if (selectedJob?.id === jobId) {
          setSelectedJob(result.job!);
        }
        
        toast.success("岗位信息更新成功", {
          description: "您的更改已保存"
        });
        
        return { success: true };
      } else {
        toast.error("岗位信息更新失败", {
          description: result.error || "请稍后重试"
        });
        
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Job fields update error:', error);
      toast.error("岗位信息更新失败", {
        description: "网络错误，请稍后重试"
      });
      
      return { success: false, error: "网络错误" };
    }
  }, [allJobs, selectedJob]);

  return (
    <div className="space-y-6">
      {/* 筛选条件和工具栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select
            value={workLocation || "all"}
            onValueChange={(value) => setWorkLocation(value === "all" ? undefined : value as WorkLocationType)}
          >
            <SelectTrigger className="w-[140px]">
              <MapPin className="w-4 h-4 mr-2" />
              <SelectValue placeholder="办公方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="remote">远程</SelectItem>
              <SelectItem value="in_person">现场</SelectItem>
              <SelectItem value="hybrid">混合</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={employmentType || "all"}
            onValueChange={(value) => setEmploymentType(value === "all" ? undefined : value as EmploymentType)}
          >
            <SelectTrigger className="w-[140px]">
              <Briefcase className="w-4 h-4 mr-2" />
              <SelectValue placeholder="职位类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="full_time">全职</SelectItem>
              <SelectItem value="part_time">兼职</SelectItem>
              <SelectItem value="co_op">Co-op</SelectItem>
              <SelectItem value="internship">实习</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 错误显示 */}
      {error && (
        <div className="text-center py-8 text-red-500">
          <p>加载职位时出错: {error}</p>
          <Button
            variant="outline"
            onClick={retry}
            className="mt-2"
          >
            重试
          </Button>
        </div>
      )}

      {/* 职位卡片网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isLoading ? (
          Array(6).fill(0).map((_, i) => (
            <Card key={i} className="p-6 space-y-4 animate-pulse">
              <div className="h-5 bg-muted rounded w-3/4" />
              <div className="h-4 bg-muted rounded w-1/2" />
              <div className="h-4 bg-muted rounded w-2/3" />
              <div className="flex gap-2">
                <div className="h-6 bg-muted rounded w-16" />
                <div className="h-6 bg-muted rounded w-12" />
              </div>
            </Card>
          ))
        ) : jobs.map((job) => {
          const updatedJob = getJobWithOptimisticUpdate(job);
          return (
            <Card 
              key={job.id}
              className="p-6 space-y-4 hover:border-gray-300 transition-colors cursor-pointer bg-white group"
              onClick={() => handleJobClick(updatedJob)}
            >
              <div className="flex justify-between items-start">
                <div className="space-y-2 flex-1 min-w-0">
                  <h3 className="font-semibold line-clamp-1 text-black">
                    {updatedJob.position_title}
                  </h3>
                  <div className="flex items-center text-gray-600">
                    <Building2 className="w-4 h-4 mr-2 text-gray-500" />
                    <span className="line-clamp-1">
                      {updatedJob.company_name}
                    </span>
                  </div>
                </div>
                <div className="flex items-start gap-2" onClick={(e) => e.stopPropagation()}>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="hidden group-hover:block h-6 w-6 text-gray-400 hover:text-red-500" 
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteJob(updatedJob.id);
                    }}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                  <StageSelector
                    currentStage={getCurrentActualStage(updatedJob.application_log || [], updatedJob.application_status) as any}
                    applicationLog={updatedJob.application_log || []}
                    onStageChange={async (stage) => {
                      await handleStageChange(updatedJob.id, stage);
                    }}
                    onCustomStageAdd={async (name) => {
                      await handleCustomStageAdd(updatedJob.id, name);
                    }}
                    onCustomStageRemove={async (name) => {
                      await handleCustomStageRemove(updatedJob.id, name);
                    }}
                    className="text-sm h-6 px-0 py-0 border-none shadow-none bg-transparent hover:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
              </div>

            <div className="space-y-2 text-sm text-gray-600">
              {updatedJob.location && (
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-gray-500" />
                  <span>{updatedJob.location}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Briefcase className="w-4 h-4 text-gray-500" />
                <span>{formatWorkLocation(updatedJob.work_location)}</span>
              </div>
              {updatedJob.salary_range && (
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-gray-500" />
                  <span>{updatedJob.salary_range}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <span>{formatDate(updatedJob.created_at)}</span>
              </div>
            </div>

            {updatedJob.keywords && updatedJob.keywords.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {updatedJob.keywords.slice(0, 3).map((keyword, index) => (
                  <Badge key={index} variant="secondary" className="text-xs bg-gray-100 text-gray-700 border-gray-200">
                    {keyword}
                  </Badge>
                ))}
                {job.keywords.length > 3 && (
                  <Badge variant="outline" className="text-xs text-gray-600 border-gray-300">
                    +{job.keywords.length - 3} 更多
                  </Badge>
                )}
              </div>
            )}
                      </Card>
          );
        })}
        </div>

      {/* 无限滚动触发器 */}
      <InfiniteScrollTrigger
        onLoadMore={loadMore}
        isLoading={isLoadingMore}
        hasMore={hasMore}
      />

      {/* 详情Modal */}
      <JobDetailsModal
        job={selectedJob}
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onStageChange={handleStageChange}
        onCustomStageAdd={handleCustomStageAdd}
        onCustomStageRemove={handleCustomStageRemove}
        onEventTimeUpdate={handleEventTimeUpdate}
        onNoteAdd={handleNoteAdd}
        onNoteRemove={handleNoteRemove}
        onJobFieldsUpdate={handleJobFieldsUpdate}
      />
    </div>
  );
} 