'use client';

import { Profile } from "@/lib/types";
import { User, Briefcase, GraduationCap, Code, Pencil } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import Link from "next/link";

interface ProfileRowProps {
  profile: Profile;
}

export function ProfileRow({ profile }: ProfileRowProps) {
  return (
    <div className="group relative">
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gray-100/10 opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-xl" />
      
      <div className="relative rounded-xl bg-white hover:bg-gray-50 backdrop-blur-xl border border-gray-200 shadow-lg transition-all duration-500 group-hover:shadow-xl group-hover:-translate-y-0.5">
        <div className="px-4 sm:px-6 py-3">
          {/* Main container - stack on mobile, row on desktop */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6 max-w-7xl mx-auto">
            {/* Left section with avatar, name and stats */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 flex-1 min-w-0">
              {/* Avatar and Name group */}
              <div className="flex items-center gap-4">
                {/* Enhanced Avatar Circle */}
                <div className="shrink-0 h-10 w-10 rounded-full bg-gray-100 from-gray-500 to-gray-500 p-[2px] shadow-xl group-hover:shadow-gray-500/25 transition-all duration-500">
                  <div className="h-full w-full rounded-full bg-gray-100 from-white to-white/90 p-1.5 flex items-center justify-center">
                    <User className="h-5 w-5 text-gray-600" />
                  </div>
                </div>

                {/* Name with enhanced gradient */}
                <h3 className="text-lg font-semibold text-gray-900 whitespace-nowrap">
                  {profile.full_name}
                </h3>
              </div>

              {/* Stats Row - hidden on mobile, visible on sm and up */}
              <div className="hidden sm:flex items-center gap-3">
                {[
                  { 
                    icon: Briefcase, 
                    label: "经验",
                    count: profile.work_experience.length,
                    colors: {
                      bg: "from-gray-50/50 to-gray-100/50",
                      text: "text-gray-700",
                      iconBg: "bg-gray-100",
                      border: "border-gray-200"
                    }
                  },
                  { 
                    icon: GraduationCap, 
                    label: "教育",
                    count: profile.education.length,
                    colors: {
                      bg: "from-gray-50/50 to-gray-100/50",
                      text: "text-gray-700",
                      iconBg: "bg-gray-100",
                      border: "border-gray-200"
                    }
                  },
                  { 
                    icon: Code, 
                    label: "项目",
                    count: profile.projects.length,
                    colors: {
                      bg: "from-gray-50/50 to-gray-100/50",
                      text: "text-gray-700",
                      iconBg: "bg-gray-100",
                      border: "border-gray-200"
                    }
                  },
                ].map((stat) => (
                  <div 
                    key={stat.label} 
                    className={cn(
                      "flex items-center gap-2 px-2.5 py-1 rounded-full",
                      "bg-gray-100 border backdrop-blur-sm",
                      "transition-all duration-500 hover:shadow-sm",
                      "hover:-translate-y-0.5",
                      stat.colors.bg,
                      stat.colors.border
                    )}
                  >
                    <div className={cn(
                      "p-1 rounded-full transition-transform duration-300",
                      stat.colors.iconBg,
                      "group-hover:scale-110"
                    )}>
                      <stat.icon className={cn("h-3 w-3", stat.colors.text)} />
                    </div>
                    <span className="text-sm whitespace-nowrap">
                      <span className={cn("font-semibold", stat.colors.text)}>{stat.count}</span>
                      <span className="text-muted-foreground ml-1.5">{stat.label}</span>
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Edit Button with enhanced styling */}
            <Link href="/profile" className="shrink-0">  
              <Button
                variant="outline"
                size="sm"
                className="w-full sm:w-auto bg-white border-gray-300 hover:border-gray-400 text-gray-700 
                           hover:text-gray-900 hover:bg-white
                           transition-all duration-500 hover:-translate-y-0.5 hover:shadow-md shadow-sm"
              >
                <Pencil className="h-3.5 w-3.5 mr-2" />
                编辑个人资料
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 