'use client';

import { useState } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>le, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { LoginForm } from "@/components/auth/login-form";
import { SignupForm } from "@/components/auth/signup-form";
import { Button } from "@/components/ui/button";
import { ArrowRight, Chrome, Loader2 } from "lucide-react";
import { AuthProvider } from "./auth-context";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { signInWithGoogle } from "@/app/auth/login/actions";

const solidClasses = {
  base: "bg-gray-600",
  hover: "hover:bg-gray-700",
  shadow: "shadow-lg shadow-gray-500/25",
  animation: "transition-all duration-300",
};

interface TabButtonProps {
  value: "login" | "signup";
  children: React.ReactNode;
}

interface AuthDialogProps {
  children?: React.ReactNode;
}

function TabButton({ value, children }: TabButtonProps) {
  return (
    <TabsTrigger 
      value={value}
      className="
        relative flex-1 h-8 px-3 text-sm font-medium rounded-md
        transition-all duration-200 ease-out
        data-[state=inactive]:text-gray-600 data-[state=inactive]:bg-transparent
        data-[state=active]:text-gray-700 data-[state=active]:bg-gray-50 data-[state=active]:shadow-sm
        data-[state=inactive]:hover:text-gray-600 data-[state=inactive]:hover:bg-gray-50/50
        border-0 shadow-none
        focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-400 focus-visible:ring-offset-0
      "
    >
      {children}
    </TabsTrigger>
  );
}

function SocialAuth() {
  const [isLoading, setIsLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      const result = await signInWithGoogle();
      if (result.success && result.url) {
        window.location.href = result.url;
      } else {
        toast.error(result.error || 'Google 登录失败');
      }
    } catch {
      toast.error('登录过程中发生错误');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-3 mt-4">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Separator className="bg-gray-200" />
        </div>
        <div className="relative flex justify-center text-xs">
          <span className="bg-white px-3 text-slate-500">
            或
          </span>
        </div>
      </div>
      <Button
        variant="outline"
        className="
          w-full h-10 bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300
          text-gray-700 font-medium transition-all duration-200
          focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
          rounded-lg hover:text-gray-900
        "
        onClick={handleGoogleSignIn}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            连接中...
          </>
        ) : (
          <>
            <Chrome className="mr-2 h-4 w-4" />
            使用 Google 继续
          </>
        )}
      </Button>
    </div>
  );
}

export function AuthDialog({ children }: AuthDialogProps) {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<"login" | "signup">("login");

  return (
    <Dialog open={open} onOpenChange={setOpen}>

      {/* AUTH DIALOG TRIGGER BUTTON */}
      <DialogTrigger asChild>
        {children || (
          <Button 
            size="lg" 
            className={`${solidClasses.base} ${solidClasses.hover} text-white font-semibold
            text-lg py-6 px-10 ${solidClasses.animation} group
            shadow-xl shadow-gray-500/30 hover:shadow-gray-500/40
            ring-2 ring-white/20 hover:ring-white/30
            scale-105 hover:scale-110 transition-all duration-300
            rounded-xl relative overflow-hidden`}
            aria-label="Open authentication dialog"
          >
            <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <span className="relative z-10 flex items-center justify-center">
              立即开始
              <ArrowRight className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </span>
          </Button>
        )}
      </DialogTrigger>

      <DialogContent
        className="
          sm:max-w-[420px] w-full max-h-[85vh] p-0 bg-white border border-gray-200 shadow-xl
          animate-in fade-in-0 zoom-in-95 duration-200
          rounded-xl overflow-hidden overflow-y-auto
        "
      >
        <AuthProvider>
          {/* Hidden accessibility elements */}
          <DialogTitle className="sr-only">身份验证</DialogTitle>
          <DialogDescription className="sr-only">登录或创建账户</DialogDescription>

          {/* Content starts immediately with tabs */}
          <div className="px-6 pt-6">
            <Tabs 
              value={activeTab} 
              onValueChange={(value) => setActiveTab(value as "login" | "signup")} 
              className="w-full"
            >
              <TabsList className="
                w-full h-10 bg-gray-50/30 border border-gray-100/50 p-1
                flex gap-0.5 rounded-lg
              ">
                <TabButton value="login">
                  登录
                </TabButton>
                <TabButton value="signup">
                  创建账户
                </TabButton>
              </TabsList>

              {/* Forms Content */}
              <div className="mt-5 pb-6">
                <TabsContent value="login" className="mt-0 space-y-4">
                  <div className="text-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-800">欢迎回来</h3>
                    <p className="text-sm text-gray-600 mt-1">登录以继续</p>
                  </div>
                  <LoginForm />
                  <SocialAuth />
                </TabsContent>
                
                <TabsContent value="signup" className="mt-0 space-y-4">
                  <div className="text-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-800">开始使用</h3>
                    <p className="text-sm text-gray-600 mt-1">创建您的免费账户</p>
                  </div>
                  <SignupForm />
                  <SocialAuth />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </AuthProvider>
      </DialogContent>
    </Dialog>
  );
} 