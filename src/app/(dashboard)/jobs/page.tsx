'use client';

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { JobListingsCard } from "@/components/jobs/job-listings-card";
import { Plus } from "lucide-react";

// 申请阶段类型（中文）
type ApplicationStage = '全部' | '申请' | '笔试' | '面试' | '谈判' | '录用' | '拒绝';

export default function JobsPage() {
  const [selectedStage, setSelectedStage] = useState<ApplicationStage>('全部');

  const handleAddJob = () => {
    // TODO: 实现添加申请功能，集成AI JD解析流程
    console.log('添加申请功能暂未实现');
  };

  return (
    <div className="container max-w-6xl mx-auto flex flex-col gap-8 p-8">
      {/* 页面标题与操作栏 - Vercel风格 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">求职申请管理</h1>
          <p className="text-muted-foreground">管理您的求职申请和面试进度</p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* 阶段筛选 */}
          <Select
            value={selectedStage}
            onValueChange={(value: ApplicationStage) => setSelectedStage(value)}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="筛选阶段" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="全部">全部</SelectItem>
              <SelectItem value="申请">申请</SelectItem>
              <SelectItem value="笔试">笔试</SelectItem>
              <SelectItem value="面试">面试</SelectItem>
              <SelectItem value="谈判">谈判</SelectItem>
              <SelectItem value="录用">录用</SelectItem>
              <SelectItem value="拒绝">拒绝</SelectItem>
            </SelectContent>
          </Select>

          {/* 添加申请按钮 */}
          <Button onClick={handleAddJob}>
            <Plus className="w-4 h-4 mr-2" />
            添加申请
          </Button>
        </div>
      </div>

      {/* 岗位列表组件 */}
      <JobListingsCard selectedStage={selectedStage} />
    </div>
  );
}
