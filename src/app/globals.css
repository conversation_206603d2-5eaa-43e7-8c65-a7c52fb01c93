@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

@layer base {
  :root {
    /* 极简黑白主题 - 专注浅色模式 */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    /* [MODIFY 2025-08-09] 全局卡片/边框清晰化：将 --border 由 0 0% 96% 调整为 0 0% 90% */
    /* 原值保留如下以便回滚： --border: 0 0% 96%; */
    --border: 0 0% 90%;
    --input: 0 0% 100%;
    --ring: 217 91% 60%;
    --radius: 0.25rem;
    
    /* 设计令牌变量 */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-md: 0.25rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    
    /* 间距变量 */
    --spacing-px: 1px;
    --spacing-0: 0;
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-7: 1.75rem;
    --spacing-8: 2rem;
    --spacing-9: 2.25rem;
    --spacing-10: 2.5rem;
    --spacing-11: 2.75rem;
    --spacing-12: 3rem;
    --spacing-14: 3.5rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    --spacing-32: 8rem;
    --spacing-40: 10rem;
    --spacing-48: 12rem;
    --spacing-56: 14rem;
    --spacing-64: 16rem;
    
    /* 阴影变量 */
    --shadow-none: 0 0 0 0 transparent;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* 动画变量 */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    --duration-slower: 700ms;
    
    /* 字体变量 */
    --font-sans: 'Inter', ui-sans-serif, system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', ui-monospace, SFMono-Regular, monospace;
    --font-serif: 'Georgia', ui-serif, serif;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* 简洁的纯色背景，保持主题一致性 */
    background-color: hsl(var(--background));
  }
}

@layer utilities {
  .glass-card {
    @apply bg-white/40 backdrop-blur-md border border-white/20 shadow-lg relative overflow-hidden;
  }
  
  .hover-card {
    @apply transition-all duration-500 hover:shadow-2xl hover:-translate-y-1 hover:bg-gray-50;
  }

  @keyframes float {
    0% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(-10px, -10px) rotate(1deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
  }

  @keyframes float-delayed {
    0% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(10px, -5px) rotate(-1deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
  }

  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 9s ease-in-out infinite;
  }

  .bg-solid-glow-blue {
    background-color: theme('colors.blue.500/0.1');
  }

  .bg-solid-glow-gray {
    background-color: theme('colors.gray.100/0.5');
  }
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
}



.buyButton {
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  color: hsl(var(--primary-foreground));
  background-color: hsl(var(--primary)); /* 品牌蓝 */
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  padding: var(--spacing-2);
  font-size: 1rem;
  letter-spacing: 0.6px;
  box-shadow: var(--shadow-sm); /* 品牌蓝阴影 */
  font-family: cursive;
}
.buyButton:hover,
.buyButton:active,
.buyButton:focus {
  text-decoration: underline;
  background-color: hsl(var(--primary) / 0.9); /* 深品牌蓝 */
  box-shadow: var(--shadow-md);
  opacity: 0.9;
  color: hsl(var(--primary-foreground));
}

.coffeeImage {
  height: 2vh;
  box-shadow: none;
  border: none;
  vertical-align: middle;
}

.coffeeButtonText {
  margin-left: 15px;
  font-size: 0.8rem;
  vertical-align: middle;
}

/* Buy Me Coffee Button Styles */
#bmc-wbtn {
  position: fixed !important;
  bottom: 1.5rem !important;
  right: 1.5rem !important;
  z-index: 50 !important;
  transition: transform 0.2s ease-in-out !important;
}

#bmc-wbtn:hover {
  transform: translateY(-2px) !important;
}

@media (max-width: 640px) {
  #bmc-wbtn {
    bottom: 1rem !important;
    right: 1rem !important;
    transform: scale(0.9) !important;
  }
}
/* 
h1 {
  display: block;
  font-size: 2em;
  margin-top: 0.67em;
  margin-bottom: 0.67em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}


h2 {
  display: block;
  font-size: 1.5em;
  margin-top: 0.83em;
  margin-bottom: 0.83em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h3 {
  display: block;
  font-size: 1.17em;
  margin-top: 1em;
  margin-bottom: 1em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h4 {
  display: block;
  font-size: 1em;
  margin-top: 1.33em;
  margin-bottom: 1.33em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h5 {
  display: block;
  font-size: .83em;
  margin-top: 1.67em;
  margin-bottom: 1.67em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
}

h6 {
  display: block;
  font-size: .67em;
  margin-top: 2.33em;
  margin-bottom: 2.33em;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold;
} */



/* 1) 取消按钮焦点轮廓（含 Tailwind ring 的 box-shadow） */
button:focus,
button:focus-visible {
  /* 原可能行为：outline / ring（box-shadow）可视化 */
  outline: none !important; /* [MODIFY 2025-08-09] 移除 outline */
  box-shadow: none !important; /* [MODIFY 2025-08-09] 移除 ring 阴影 */
}

/* 2) 悬停不改变按钮背景色：
   仅添加说明，不强制覆盖 background-color，避免破坏基础底色。
   统一规范由组件与调用处清理 `hover:bg-*` 实现。 */