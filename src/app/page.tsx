import { Background } from "@/components/landing/Background";
import FeatureHighlights from "@/components/landing/FeatureHighlights";
import { <PERSON> } from "@/components/landing/Hero";
import { PricingPlans } from "@/components/landing/PricingPlans";
import { VideoShowcase } from "@/components/landing/VideoShowcase";
import { CreatorStory } from "@/components/landing/creator-story";
import { FAQ } from "@/components/landing/FAQ";
import { Footer } from "@/components/layout/footer";
import { NavLinks } from "@/components/layout/nav-links";
import { Logo } from "@/components/ui/logo";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { Metadata } from "next";
import Script from "next/script";

// Page-specific metadata that extends the base metadata from layout.tsx
export const metadata: Metadata = {
  title: "SnapOffer - AI求职智能体 - 智能Offer收割机",
  description: "集成智能求职助手、简历编辑器、求职管理、岗位评估、求职信生成和应聘攻略，帮助求职者快速理解和评估岗位，快速获得Offer。",
  openGraph: {
    title: "SnapOffer - AI求职智能体 - 智能Offer收割机",
    description: "集成智能求职助手、简历编辑器、求职管理、岗位评估、求职信生成和应聘攻略，帮助求职者快速理解和评估岗位，快速获得Offer。",
    url: "https://SnapOffer.com",
  },
  twitter: {
    title: "SnapOffer - AI求职智能体 - 智能Offer收割机",
    description: "集成智能求职助手、简历编辑器、求职管理、岗位评估、求职信生成和应聘攻略，帮助求职者快速理解和评估岗位，快速获得Offer。",
  },
};

export default async function Page() {
  // Check if user is authenticated
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  // If user is authenticated, redirect to home page
  if (user) {
    redirect("/home");
  }
  
  return (
    <>
      {/* JSON-LD structured data for SEO */}
      <Script
        id="schema-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "SnapOffer",
            "applicationCategory": "BusinessApplication",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "description": "智能求职管理工具，快速为目标岗位定制简历和应聘攻略，帮助求职者快速理解和评估岗位，快速获得Offer。",
            "operatingSystem": "Web",
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "ratingCount": "500"
            }
          })
        }}
      />
    
      <main aria-label="SnapOffer 首页" className=" ">
        {/* Simplified Navigation */}
        <nav aria-label="主导航" className="border-b border-gray-200 fixed top-0 w-full bg-white/95 z-[1000] transition-all duration-300 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Logo />
              <NavLinks />
            </div>
          </div>
        </nav>
        
        {/* Background component */}
        <Background />
        
        {/* Main content */}
        <div className="relative z-10 mx-auto px-4 sm:px-6 lg:px-24 pt-8">
          {/* Hero Section */}
          <Hero />
        </div>
        
        {/* Video Showcase Section */}
        <section id="product-demo">
          <VideoShowcase />
        </section>
        
        {/* Feature Highlights Section */}
        <section id="features" aria-labelledby="功能特色标题">
          <FeatureHighlights />
        </section>
        
        {/* Creator Story Section */}
        <section id="about" aria-labelledby="关于我们标题">
          <CreatorStory />
        </section>
        
        {/* Pricing Plans Section */}
        <section id="pricing" aria-labelledby="价格方案标题">
          <PricingPlans />
        </section>
        
        {/* FAQ Section */}
        <FAQ />

        <Footer variant="static"/>
      </main>
    </>
  );
}