import { getAllPosts } from "@/lib/blog";
import Link from "next/link";
import { Metadata } from "next";
import { Calendar, ArrowRight, Sparkles } from "lucide-react";

export const metadata: Metadata = {
  title: "SnapOffer 博客",
  description: "在我们利用人工智能彻底改变求职方式的过程中，探索我们团队的最新提示、产品更新和见解。",
};

export default async function BlogPage() {
  const posts = await getAllPosts();

  return (
    <main className="min-h-screen relative">
      {/* 简化的背景 */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gray-50/30" />
        <div className="absolute inset-0 bg-[linear-gradient(to_right,hsl(var(--border))_1px,transparent_1px),linear-gradient(to_bottom,hsl(var(--border))_1px,transparent_1px)] bg-[size:14px_24px]" />
        {/* 装饰圆形 */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gray-100/15 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gray-100/15 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-16 max-w-5xl">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-muted from-muted to-muted rounded-full text-sm font-medium text-muted-foreground mb-6">
            <Sparkles className="w-4 h-4" />
            最新见解和更新
          </div>
          <h1 className="text-5xl md:text-6xl font-bold mb-6 text-foreground leading-tight">
            SnapOffer 博客
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            在我们利用人工智能彻底改变求职方式的过程中，探索我们团队的最新提示、产品更新和见解。
          </p>
        </div>

        {/* Articles Grid */}
        <div className="grid gap-8 md:gap-12">
          {posts.map(({ slug, frontMatter }, index) => (
            <article key={slug} className={`group ${index === 0 ? 'md:col-span-2' : ''}`}>
              <Link
                href={`/blog/${slug}`}
                className="block"
              >
                <div className="relative overflow-hidden rounded-2xl bg-card/80 backdrop-blur-xl border border-border shadow-lg hover:shadow-2xl transition-all duration-500 group-hover:scale-[1.02] group-hover:border-primary/60">
                  {/* Gradient border effect */}
                  <div className="absolute inset-0 bg-gray-100 from-gray-400/10 via-cyan-400/10 to-gray-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  
                  <div className="relative p-8 md:p-10">
                    {/* Featured badge for first post */}
                    {index === 0 && (
                      <div className="inline-flex items-center gap-1 px-3 py-1 bg-primary text-primary-foreground text-xs font-semibold rounded-full mb-4">
                        <Sparkles className="w-3 h-3" />
                        特色
                      </div>
                    )}
                    
                    {/* Date */}
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                      <Calendar className="w-4 h-4" />
                      <time dateTime={frontMatter.date}>
                        {new Date(frontMatter.date).toLocaleDateString(undefined, {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                      </time>
                    </div>

                    {/* Title */}
                    <h2 className={`font-bold mb-4 text-foreground group-hover:text-primary transition-colors duration-300 ${
                      index === 0 ? 'text-3xl md:text-4xl' : 'text-2xl'
                    }`}>
                      {frontMatter.title}
                    </h2>

                    {/* Description */}
                    <p className={`text-muted-foreground leading-relaxed mb-6 ${
                      index === 0 ? 'text-lg' : 'text-base'
                    }`}>
                      {frontMatter.description}
                    </p>

                    {/* Read more link */}
                    <div className="flex items-center gap-2 text-primary font-semibold group-hover:text-primary/80 transition-colors duration-300">
                      <span>阅读文章</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </Link>
            </article>
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-20">
          <div className="inline-block p-8 rounded-2xl bg-muted border border-primary/20">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              想要了解更多？
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              跟着我们一起探索，我们将继续创新和改进求职智能体验。
            </p>
            <Link
              href="/"
              className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground font-semibold rounded-lg hover:shadow-lg transition-all duration-300"
            >
              尝试 SnapOffer
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
} 