import { Metadata } from "next";
import { MockResume } from "@/components/landing/mock-resume";
import { MockResumeMobile } from "@/components/landing/mock-resume-mobile";
import { BenefitsList } from "@/components/landing/benefits-list";
import { ActionButtons } from "@/components/landing/action-buttons";
import { Logo } from "@/components/ui/logo";
import { PricingSection } from "@/components/landing/pricing-section";
import { ErrorDialog } from "@/components/auth/error-dialog";
import { CreatorStory } from "@/components/landing/creator-story";
import { HowItWorks } from "@/components/landing/how-it-works";
import { HeroVideoSection } from "@/components/landing/hero-video-section";
import { Footer } from "@/components/layout/footer";
import { SplitContent } from "@/components/ui/split-content";
import { NavLinks } from "@/components/layout/nav-links";
import { ModelShowcase } from "@/components/landing/model-showcase";

// import { WaitlistSection } from "@/components/waitlist/waitlist-section";

export const metadata: Metadata = {
  title: "登录 | SnapOffer - AI求职智能体",
  description: "AI驱动的求职智能体，快速为目标岗位定制简历和应聘攻略，帮助求职者快速获得Offer。",
  keywords: ["AI求职智能体", "简历构建器", "应聘攻略", "求职管理", "岗位分析", "Offer收割机", "智能求职"],
  authors: [{ name: "SnapOffer" }],
  openGraph: {
    title: "SnapOffer - AI求职智能体",
    description: "AI驱动的求职智能体，快速为目标岗位定制简历和应聘攻略，帮助求职者快速获得Offer。",
    url: "https://SnapOffer.com//",
    siteName: "SnapOffer",
    images: [
      {
        url: "/og.webp",
        width: 1200,
        height: 630,
        alt: "SnapOffer - AI求职智能体",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "SnapOffer - AI求职智能体",
    description: "AI驱动的求职智能体，快速为目标岗位定制简历和应聘攻略，帮助求职者快速获得Offer。",
    images: ["/og.webp"],
    creator: "@SnapOffer",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  // verification: {
  //   google: "google-site-verification-code", // Replace with actual verification code
  // },
};

export default async function LoginPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const params = await searchParams;
  const showErrorDialog = params?.error === 'email_confirmation' || params?.error === 'auth_code_missing';

  return (
    <>
      <main className="relative overflow-x-hidden selection:bg-gray-200/50 ">
        {/* Error Dialog */}
        <ErrorDialog isOpen={!!showErrorDialog} />

       

        {/* Enhanced Navigation with backdrop blur and border */}
        <nav className="border-b border-white/50 backdrop-blur-xl shadow-md fixed top-0 w-full bg-white/20 z-[1000] transition-all duration-500">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Logo />
              <NavLinks />
            </div>
          </div>
        </nav>

        {/* Enhanced Content with better spacing and animations */}
        <div className="relative z-10">
          
          {/* Hero Section with Split Layout */}
          <div className="mb-24 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16">
            <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 py-12 lg:py-24 items-center">
              {/* Left Column - Content */}
              <div className="flex flex-col gap-8 lg:gap-12 lg:pr-12">
                <div className="space-y-8">
                  <div className="space-y-5">
                    <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold tracking-tight leading-tight">
                      <span className="inline-block text-gray-600 pb-2">
                        AI求职智能体
                      </span>
                      <br />
                      <span className="relative inline-block">
                        <span className="inline-block text-gray-600">
                          智能Offer收割机
                        </span>
                        <div className="absolute -bottom-2 left-0 w-24 sm:w-32 h-1.5 bg-gray-600 rounded-full" />
                      </span>
                    </h1>
                    
                    <p className="text-xl sm:text-2xl text-muted-foreground/90 leading-relaxed max-w-2xl font-medium">
                      快速为目标岗位定制简历和应聘攻略，帮助求职者快速获得Offer。
                    </p>
                  </div>

                  <BenefitsList />
                </div>
                
                <ActionButtons />
              </div>

              {/* Right Column - Floating Resume Preview */}
              <div className="relative mt-8 lg:mt-0">
                {/* Mobile-only single resume view */}
                <div className="block lg:hidden">
                  <div className="relative w-full max-w-[min(85vw,_6in)] mx-auto transform hover:scale-[1.02] transition-transform duration-700">
                    {/* Decorative Elements - Enhanced gradients for mobile */}
                    <div className="absolute inset-0 bg-gray-100 from-gray-500/5 to-blue-500/5 rounded-sm transform rotate-3 scale-[1.03] shadow-xl" />
                    <div className="absolute inset-0 bg-gray-100 from-gray-500/5 to-blue-500/5 rounded-sm transform -rotate-3 scale-[1.03] shadow-xl" />
                    
                    {/* Stacked Resume Previews - Mobile Optimized */}
                    <div className="relative">
                      {/* Background Resume - Third Layer */}
                      <div className="absolute -right-5 top-3 opacity-80 scale-[0.98] rotate-[-8deg] shadow-lg">
                        <MockResumeMobile />
                      </div>
                      
                      {/* Middle Resume - Second Layer */}
                      <div className="absolute -right-2.5 top-1.5 opacity-90 scale-[0.99] rotate-[-4deg] origin-center shadow-lg">
                        <MockResumeMobile />
                      </div>

                      {/* Front Resume - Main Layer */}
                      <div className="relative shadow-xl transform transition-all duration-700 hover:translate-y-[-5px]">
                        <MockResumeMobile />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Desktop stacked resume view */}
                <div className="relative hidden lg:block transform hover:scale-[1.02] transition-transform duration-700">
                  {/* 装饰元素 - 纯色设计 */}
                  <div className="absolute inset-0 bg-gray-100/20 rounded-3xl transform rotate-6 scale-110 shadow-xl" />
                  <div className="absolute inset-0 bg-gray-100/20 rounded-3xl transform -rotate-6 scale-110 shadow-xl" />

                  {/* 简化的光晕效果 */}
                  <div className="absolute -inset-0.5 bg-gray-200/30 rounded-3xl blur-xl opacity-70 group-hover:opacity-100 transition duration-1000"></div>
                  
                  {/* Stacked Resume Previews with improved positioning and effects */}
                  <div className="relative">
                    {/* Background Resume - Third Layer */}
                    <div className="absolute -right-14 top-6 opacity-70 blur-[0.5px] scale-[0.96] rotate-[-10deg] origin-bottom-right shadow-xl">
                      <MockResume />
                    </div>
                    
                    {/* Middle Resume - Second Layer */}
                    <div className="absolute -right-7 top-3 opacity-85 scale-[0.98] rotate-[-5deg] origin-bottom-right shadow-xl">
                      <MockResume />
                    </div>

                    {/* Front Resume - Main Layer */}
                    <div className="relative shadow-2xl transform transition-all duration-700 hover:translate-y-[-5px]">
                      <MockResume />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Enhanced Hero Video Section with better framing */}
          <div className="relative py-16">
            <div className="absolute inset-0 bg-gray-100 from-transparent via-gray-50/30 to-transparent"></div>
            <HeroVideoSection />
          </div>
          
          {/* Enhanced Model Showcase with better spacing */}
          <div className="py-16 relative">
            <div className="absolute inset-0 bg-gray-100 from-transparent via-gray-50/30 to-transparent"></div>
            <ModelShowcase />
          </div>

          {/* Enhanced Features Section with improved card styling */}
          <div className="flex flex-col gap-24 py-24 relative" id="features">
            <div className="absolute inset-0 bg-gray-100 from-transparent via-gray-50/30 to-transparent"></div>
            
            <SplitContent
              imageSrc="/SS Chat.png"
              heading="AI求职智能助手"
              description="从我们的高级AI助手获得实时反馈和建议。快速分析目标岗位要求，为您定制最合适的简历和应聘策略，确保在众多求职者中脱颖而出。"
              imageOnLeft={false}
              imageOverflowRight={true}
            />

            <SplitContent
              imageSrc="/Dashboard Image.png"
              heading="智能求职管理中心"
              description="在我们的直观仪表板中管理您的所有求职活动。创建基础简历，为特定岗位生成定制版本，轻松跟踪您的申请进度。"
              imageOnLeft={true}
            />

            <SplitContent
              imageSrc="/SS Score.png"
              heading="智能岗位评估"
              description="通过我们的综合评分系统，深入了解您的求职效果。跟踪关键指标，确定改进领域，优化您的求职策略，在雇主和ATS系统中脱颖而出。"
              imageOnLeft={false}
              imageOverflowRight={true}
            />

            <SplitContent
              imageSrc="/SS Cover Letter.png"
              heading="AI应聘攻略生成器"
              description="在几分钟内创建引人注目的个性化应聘攻略。为特定岗位机会定制您的消息，保持专业和吸引人的语调，获得关注。"
              imageOnLeft={true}
            />
          </div>

          {/* How It Works Section with improved framing */}
          <div id="how-it-works" className="relative py-16">
            <div className="absolute inset-0 bg-gray-100 from-transparent via-blue-50/30 to-transparent"></div>
            <HowItWorks />
          </div>

          {/* Pricing Section with improved framing */}
          <div id="pricing" className="relative py-16">
            <div className="absolute inset-0 bg-gray-100 from-transparent via-gray-50/30 to-transparent"></div>
            <PricingSection />
          </div>

          {/* Creator Story with improved framing */}
          <div id="about" className="relative py-16">
            <div className="absolute inset-0 bg-gray-100 from-transparent via-gray-50/30 to-transparent"></div>
            <CreatorStory />
          </div>
        </div>
      </main>
      <Footer variant="static"/>
    </>
  );
}
