import { useState, useCallback, useRef, useEffect } from 'react';

export interface InfiniteScrollOptions<T> {
  /** 初始页面大小 */
  pageSize?: number;
  /** 获取数据的函数 */
  fetchData: (page: number, pageSize: number) => Promise<{
    data: T[];
    totalCount: number;
    hasMore: boolean;
  }>;
  /** 依赖项数组，当这些值变化时重置数据 */
  dependencies?: any[];
  /** 是否启用无限滚动 */
  enabled?: boolean;
}

export interface InfiniteScrollResult<T> {
  /** 累积的数据列表 */
  data: T[];
  /** 是否正在加载初始数据 */
  isLoading: boolean;
  /** 是否正在加载更多数据 */
  isLoadingMore: boolean;
  /** 是否还有更多数据可加载 */
  hasMore: boolean;
  /** 加载更多数据的函数 */
  loadMore: () => void;
  /** 重置数据并重新开始的函数 */
  reset: () => void;
  /** 错误状态 */
  error: string | null;
  /** 总数据量 */
  totalCount: number;
}

/**
 * 无限滚动自定义Hook
 * 提供数据累积加载、滚动检测和状态管理功能
 */
export function useInfiniteScroll<T>({
  pageSize = 6,
  fetchData,
  dependencies = [],
  enabled = true
}: InfiniteScrollOptions<T>): InfiniteScrollResult<T> {
  const [data, setData] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  
  // 使用ref来避免在useCallback中的依赖问题
  const currentPageRef = useRef(currentPage);
  const dataRef = useRef(data);
  const hasMoreRef = useRef(hasMore);
  
  // 更新refs
  useEffect(() => {
    currentPageRef.current = currentPage;
    dataRef.current = data;
    hasMoreRef.current = hasMore;
  }, [currentPage, data, hasMore]);

  // 加载数据的核心函数
  const loadData = useCallback(async (page: number, isLoadMore = false) => {
    if (!enabled) return;
    
    try {
      if (isLoadMore) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      const result = await fetchData(page, pageSize);
      
      setTotalCount(result.totalCount);
      setHasMore(result.hasMore);
      
      if (isLoadMore) {
        // 累积模式：追加新数据
        setData(prevData => [...prevData, ...result.data]);
      } else {
        // 初始加载或重置：替换数据
        setData(result.data);
      }
      
      setCurrentPage(page);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载数据失败';
      setError(errorMessage);
      console.error('Error loading data:', err);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  }, [fetchData, pageSize, enabled]);

  // 加载更多数据
  const loadMore = useCallback(() => {
    if (!hasMoreRef.current || isLoadingMore || isLoading) {
      return;
    }
    
    const nextPage = currentPageRef.current + 1;
    loadData(nextPage, true);
  }, [loadData, isLoadingMore, isLoading]);

  // 重置数据
  const reset = useCallback(() => {
    setData([]);
    setCurrentPage(1);
    setHasMore(true);
    setError(null);
    setTotalCount(0);
    loadData(1, false);
  }, [loadData]);

  // 当依赖项变化时重置数据
  useEffect(() => {
    if (enabled) {
      reset();
    }
  }, [...dependencies, enabled]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    data,
    isLoading,
    isLoadingMore,
    hasMore,
    loadMore,
    reset,
    error,
    totalCount
  };
}
