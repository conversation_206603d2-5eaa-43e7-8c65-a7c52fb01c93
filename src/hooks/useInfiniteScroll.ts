import { useState, useCallback, useRef, useEffect } from 'react';

interface UseInfiniteScrollOptions<T, C = string> {
  initialData?: T[];
  fetchFunction: (cursor?: C) => Promise<{
    data: T[];
    hasMore: boolean;
    nextCursor: C | null;
  }>;
  getItemId: (item: T) => string; // Function to extract unique ID from item
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  error: string | null;
  loadMore: () => Promise<void>;
  reset: () => void;
  refresh: () => Promise<void>;
  retry: () => Promise<void>;
  updateItem: (id: string, updatedItem: T) => void;
}

export function useInfiniteScroll<T, C = string>({
  initialData = [],
  fetchFunction,
  getItemId
}: UseInfiniteScrollOptions<T, C>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nextCursor, setNextCursor] = useState<C | null>(null);
  
  // Prevent multiple simultaneous requests
  const isLoadingRef = useRef(false);

  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !hasMore) return;
    
    isLoadingRef.current = true;
    setIsLoadingMore(true);
    setError(null);

    try {
      const result = await fetchFunction(nextCursor || undefined);
      
      setData(prevData => [...prevData, ...result.data]);
      setHasMore(result.hasMore);
      setNextCursor(result.nextCursor);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load more data');
    } finally {
      setIsLoadingMore(false);
      isLoadingRef.current = false;
    }
  }, [fetchFunction, nextCursor, hasMore]);

  const reset = useCallback(() => {
    setData([]);
    setIsLoading(false);
    setIsLoadingMore(false);
    setHasMore(true);
    setError(null);
    setNextCursor(null);
    isLoadingRef.current = false;
  }, []);

  const refresh = useCallback(async () => {
    if (isLoadingRef.current) return;

    isLoadingRef.current = true;
    setIsLoading(true);
    setError(null);

    // Reset state
    setData([]);
    setHasMore(true);
    setNextCursor(null);

    try {
      const result = await fetchFunction();

      setData(result.data);
      setHasMore(result.hasMore);
      setNextCursor(result.nextCursor);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh data');
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [fetchFunction]);

  const updateItem = useCallback((id: string, updatedItem: T) => {
    setData(prevData =>
      prevData.map(item =>
        getItemId(item) === id ? updatedItem : item
      )
    );
  }, [getItemId]);

  const retry = useCallback(async () => {
    if (error || !hasMore) {
      // Always refresh on retry to get latest data
      await refresh();
    }
  }, [error, hasMore, refresh]);

  // Auto-load initial data
  useEffect(() => {
    if (data.length === 0 && !isLoading && !error) {
      refresh();
    }
  }, [data.length, isLoading, error, refresh]);

  return {
    data,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    loadMore,
    reset,
    refresh,
    retry,
    updateItem
  };
}
