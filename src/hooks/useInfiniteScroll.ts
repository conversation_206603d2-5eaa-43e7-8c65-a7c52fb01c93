import { useState, useCallback, useRef, useEffect } from 'react';

interface UseInfiniteScrollOptions<T> {
  initialData?: T[];
  pageSize?: number;
  fetchFunction: (cursor?: string) => Promise<{
    data: T[];
    hasMore: boolean;
    nextCursor: string | null;
  }>;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  error: string | null;
  loadMore: () => Promise<void>;
  reset: () => void;
  refresh: () => Promise<void>;
  retry: () => Promise<void>;
}

export function useInfiniteScroll<T>({
  initialData = [],
  pageSize = 6,
  fetchFunction
}: UseInfiniteScrollOptions<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  
  // Prevent multiple simultaneous requests
  const isLoadingRef = useRef(false);

  const loadMore = useCallback(async () => {
    if (isLoadingRef.current || !hasMore) return;
    
    isLoadingRef.current = true;
    setIsLoadingMore(true);
    setError(null);

    try {
      const result = await fetchFunction(nextCursor || undefined);
      
      setData(prevData => [...prevData, ...result.data]);
      setHasMore(result.hasMore);
      setNextCursor(result.nextCursor);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load more data');
    } finally {
      setIsLoadingMore(false);
      isLoadingRef.current = false;
    }
  }, [fetchFunction, nextCursor, hasMore]);

  const reset = useCallback(() => {
    setData([]);
    setIsLoading(false);
    setIsLoadingMore(false);
    setHasMore(true);
    setError(null);
    setNextCursor(null);
    isLoadingRef.current = false;
  }, []);

  const refresh = useCallback(async () => {
    if (isLoadingRef.current) return;

    isLoadingRef.current = true;
    setIsLoading(true);
    setError(null);

    // Reset state
    setData([]);
    setHasMore(true);
    setNextCursor(null);

    try {
      const result = await fetchFunction();

      setData(result.data);
      setHasMore(result.hasMore);
      setNextCursor(result.nextCursor);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh data');
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [fetchFunction]);

  const retry = useCallback(async () => {
    if (error) {
      // If there's an error, try to refresh
      await refresh();
    } else if (!hasMore) {
      // If no more data, try to load more anyway (in case data was added)
      await loadMore();
    }
  }, [error, hasMore, refresh, loadMore]);

  // Auto-load initial data
  useEffect(() => {
    if (data.length === 0 && !isLoading && !error) {
      refresh();
    }
  }, [data.length, isLoading, error, refresh]);

  return {
    data,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    loadMore,
    reset,
    refresh,
    retry
  };
}
