/**
 * SnapOffer 设计令牌系统 - 极简扁平设计
 * 专注浅色模式：85% 黑白 + 5% 灰色 + 10% 品牌蓝
 * 基于 shadcn/ui 和 Vercel 设计风格
 */

// 基础设计令牌类型
export interface DesignTokens {
  colors: ColorTokens;
  spacing: SpacingTokens;
  borderRadius: BorderRadiusTokens;
  shadows: ShadowTokens;
  typography: TypographyTokens;
  animations: AnimationTokens;
}

// 颜色令牌
export interface ColorTokens {
  // 基础色
  white: string;
  black: string;
  
  // 灰色系 (5% 使用比例)
  gray: {
    50: string;   // 极浅灰 - 边框、分割线
    100: string;  // 浅灰 - 禁用状态
    200: string;  // 中浅灰 - 装饰线条
    300: string;  // 中灰 - 次要文字
    400: string;  // 深灰 - 次要标题
    500: string;  // 更深灰 - 主要文字深色
    600: string;  // 深灰 - 标题
    700: string;  // 更深灰 - 强调文字
    800: string;  // 极深灰 - 替代纯黑
    900: string;  // 最深灰 - 特殊用途
  };
  
  // 品牌蓝 (10% 使用比例)
  blue: {
    50: string;   // 极浅蓝 - 背景点缀
    100: string;  // 浅蓝 - 悬停背景
    200: string;  // 中浅蓝 - 选中状态
    300: string;  // 中蓝 - 链接
    400: string;  // 亮蓝 - 主要交互
    500: string;  // 主品牌蓝 - CTA按钮
    600: string;  // 深蓝 - 按下状态
    700: string;  // 更深蓝 - 特殊强调
    800: string;  // 极深蓝 - 标题强调
    900: string;  // 最深蓝 - 特殊用途
  };
  
  // 功能色 (极少使用)
  red: {
    500: string;  // 红色 - 删除操作
    600: string;  // 深红 - 删除悬停
  };
  
  green: {
    500: string;  // 绿色 - 成功状态
    600: string;  // 深绿 - 成功悬停
  };
  
  yellow: {
    500: string;  // 黄色 - 警告状态
    600: string;  // 深黄 - 警告悬停
  };
  
  // 透明度级别
  opacity: {
    low: number;      // 0.1 - 极低透明度
    medium: number;   // 0.3 - 中等透明度
    high: number;     // 0.6 - 高透明度
    full: number;     // 1.0 - 不透明
  };
}

// 间距令牌
export interface SpacingTokens {
  px: string;        // 1px - 边框宽度
  0: string;         // 0 - 无间距
  1: string;         // 0.25rem - 4px
  2: string;         // 0.5rem - 8px
  3: string;         // 0.75rem - 12px
  4: string;         // 1rem - 16px
  5: string;         // 1.25rem - 20px
  6: string;         // 1.5rem - 24px
  8: string;         // 2rem - 32px
  10: string;        // 2.5rem - 40px
  12: string;        // 3rem - 48px
  16: string;        // 4rem - 64px
  20: string;        // 5rem - 80px
  24: string;        // 6rem - 96px
  32: string;        // 8rem - 128px
  40: string;        // 10rem - 160px
  48: string;        // 12rem - 192px
  56: string;        // 14rem - 224px
  64: string;        // 16rem - 256px
}

// 圆角令牌
export interface BorderRadiusTokens {
  none: string;      // 0 - 无圆角
  sm: string;        // 0.125rem - 2px
  md: string;        // 0.25rem - 4px
  lg: string;        // 0.5rem - 8px
  xl: string;        // 0.75rem - 12px
  '2xl': string;     // 1rem - 16px
  '3xl': string;     // 1.5rem - 24px
  full: string;      // 9999px - 完全圆形
}

// 阴影令牌
export interface ShadowTokens {
  none: string;      // 无阴影
  sm: string;        // 小阴影
  md: string;        // 中阴影
  lg: string;        // 大阴影
  xl: string;        // 超大阴影
  '2xl': string;     // 极大阴影
}

// 字体令牌
export interface TypographyTokens {
  fontFamily: {
    sans: string[];
    mono: string[];
    serif: string[];
  };
  
  fontSize: {
    xxs: string;      // 0.625rem - 10px
    xs: string;       // 0.75rem - 12px
    sm: string;       // 0.875rem - 14px
    base: string;     // 1rem - 16px
    lg: string;       // 1.125rem - 18px
    xl: string;       // 1.25rem - 20px
    '2xl': string;    // 1.5rem - 24px
    '3xl': string;    // 1.875rem - 30px
    '4xl': string;    // 2.25rem - 36px
    '5xl': string;    // 3rem - 48px
    '6xl': string;    // 3.75rem - 60px
  };
  
  fontWeight: {
    light: number;    // 300
    normal: number;   // 400
    medium: number;   // 500
    semibold: number; // 600
    bold: number;     // 700
    extrabold: number; // 800
  };
  
  lineHeight: {
    tight: number;    // 1.25
    normal: number;   // 1.5
    relaxed: number;  // 1.75
    loose: number;    // 2.0
  };
  
  letterSpacing: {
    tight: string;    // -0.025em
    normal: string;   // 0
    wide: string;     // 0.025em
    wider: string;    // 0.05em
    widest: string;   // 0.1em
  };
}

// 动画令牌
export interface AnimationTokens {
  duration: {
    fast: string;     // 150ms
    normal: string;   // 300ms
    slow: string;     // 500ms
    slower: string;   // 700ms
  };
  
  easing: {
    linear: string;   // linear
    ease: string;     // ease
    easeIn: string;   // ease-in
    easeOut: string;  // ease-out
    easeInOut: string; // ease-in-out
  };
}

// 完整的设计令牌配置
export const designTokens: DesignTokens = {
  colors: {
    // 基础色 (85% 使用比例)
    white: '#ffffff',
    black: '#000000',
    
    // 灰色系 (5% 使用比例)
    gray: {
      50: '#f9f9f9',   // 极浅灰 - 边框、分割线
      100: '#f5f5f5',  // 浅灰 - 禁用状态
      200: '#e5e5e5',  // 中浅灰 - 装饰线条
      300: '#d4d4d4',  // 中灰 - 次要文字
      400: '#a3a3a3',  // 深灰 - 次要标题
      500: '#737373',  // 更深灰 - 主要文字深色
      600: '#525252',  // 深灰 - 标题
      700: '#404040',  // 更深灰 - 强调文字
      800: '#262626',  // 极深灰 - 替代纯黑
      900: '#171717',  // 最深灰 - 特殊用途
    },
    
    // 品牌蓝 (10% 使用比例)
    blue: {
      50: '#eff6ff',   // 极浅蓝 - 背景点缀
      100: '#dbeafe',  // 浅蓝 - 悬停背景
      200: '#bfdbfe',  // 中浅蓝 - 选中状态
      300: '#93c5fd',  // 中蓝 - 链接
      400: '#60a5fa',  // 亮蓝 - 主要交互
      500: '#3b82f6',  // 主品牌蓝 - CTA按钮
      600: '#2563eb',  // 深蓝 - 按下状态
      700: '#1d4ed8',  // 更深蓝 - 特殊强调
      800: '#1e40af',  // 极深蓝 - 标题强调
      900: '#1e3a8a',  // 最深蓝 - 特殊用途
    },
    
    // 功能色 (极少使用)
    red: {
      500: '#ef4444',  // 红色 - 删除操作
      600: '#dc2626',  // 深红 - 删除悬停
    },
    
    green: {
      500: '#22c55e',  // 绿色 - 成功状态
      600: '#16a34a',  // 深绿 - 成功悬停
    },
    
    yellow: {
      500: '#eab308',  // 黄色 - 警告状态
      600: '#ca8a04',  // 深黄 - 警告悬停
    },
    
    // 透明度级别
    opacity: {
      low: 0.1,        // 极低透明度
      medium: 0.3,     // 中等透明度
      high: 0.6,       // 高透明度
      full: 1.0,       // 不透明
    },
  },
  
  spacing: {
    px: '1px',         // 1px - 边框宽度
    0: '0',            // 0 - 无间距
    1: '0.25rem',      // 0.25rem - 4px
    2: '0.5rem',       // 0.5rem - 8px
    3: '0.75rem',      // 0.75rem - 12px
    4: '1rem',         // 1rem - 16px
    5: '1.25rem',      // 1.25rem - 20px
    6: '1.5rem',       // 1.5rem - 24px
    8: '2rem',         // 2rem - 32px
    10: '2.5rem',      // 2.5rem - 40px
    12: '3rem',        // 3rem - 48px
    16: '4rem',        // 4rem - 64px
    20: '5rem',        // 5rem - 80px
    24: '6rem',        // 6rem - 96px
    32: '8rem',        // 8rem - 128px
    40: '10rem',       // 10rem - 160px
    48: '12rem',       // 12rem - 192px
    56: '14rem',       // 14rem - 224px
    64: '16rem',       // 16rem - 256px
  },
  
  borderRadius: {
    none: '0',         // 无圆角
    sm: '0.125rem',    // 0.125rem - 2px
    md: '0.25rem',     // 0.25rem - 4px
    lg: '0.5rem',      // 0.5rem - 8px
    xl: '0.75rem',     // 0.75rem - 12px
    '2xl': '1rem',     // 1rem - 16px
    '3xl': '1.5rem',   // 1.5rem - 24px
    full: '9999px',    // 完全圆形
  },
  
  shadows: {
    none: 'none',      // 无阴影
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',                              // 小阴影
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',                           // 中阴影
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',                          // 大阴影
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)',                         // 超大阴影
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',                    // 极大阴影
  },
  
  typography: {
    fontFamily: {
      sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'sans-serif'],
      mono: ['JetBrains Mono', 'ui-monospace', 'SFMono-Regular', 'monospace'],
      serif: ['Georgia', 'ui-serif', 'serif'],
    },
    
    fontSize: {
      xxs: '0.625rem',  // 10px
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem', // 60px
    },
    
    fontWeight: {
      light: 300,       // 300
      normal: 400,      // 400
      medium: 500,      // 500
      semibold: 600,    // 600
      bold: 700,        // 700
      extrabold: 800,   // 800
    },
    
    lineHeight: {
      tight: 1.25,      // 1.25
      normal: 1.5,      // 1.5
      relaxed: 1.75,    // 1.75
      loose: 2.0,       // 2.0
    },
    
    letterSpacing: {
      tight: '-0.025em', // -0.025em
      normal: '0',       // 0
      wide: '0.025em',   // 0.025em
      wider: '0.05em',   // 0.05em
      widest: '0.1em',   // 0.1em
    },
  },
  
  animations: {
    duration: {
      fast: '150ms',     // 150ms
      normal: '300ms',   // 300ms
      slow: '500ms',     // 500ms
      slower: '700ms',   // 700ms
    },
    
    easing: {
      linear: 'linear',   // linear
      ease: 'ease',       // ease
      easeIn: 'ease-in',   // ease-in
      easeOut: 'ease-out',  // ease-out
      easeInOut: 'ease-in-out', // ease-in-out
    },
  },
};

// 颜色使用规则
export const colorUsageRules = {
  // 使用比例限制
  usageRatio: {
    blackWhite: 85,   // 黑白占比
    gray: 5,          // 灰色占比
    blue: 10,         // 品牌蓝占比
    red: 0,           // 红色占比（极少）
    green: 0,         // 绿色占比（极少）
    yellow: 0,        // 黄色占比（极少）
  },
  
  // 品牌蓝使用场景
  blueUsageScenarios: [
    'primary-cta-button',
    'links',
    'selected-state',
    'focus-ring',
    'loading-indicator',
    'active-state',
  ],
  
  // 禁止使用品牌蓝的场景
  blueRestrictedScenarios: [
    'backgrounds',
    'card-backgrounds',
    'secondary-buttons',
    'decorative-elements',
    'text-color-except-links',
  ],
  
  // 对比度要求
  contrastRatio: {
    bodyText: 21,      // 正文文字对比度
    secondaryText: 4.5, // 次要文字对比度
    links: 4.5,        // 链接对比度
    buttons: 4.5,      // 按钮文字对比度
  },
} as const;

// 工具函数
export const designTokenUtils = {
  // 获取颜色
  getColor: (colorPath: string): string => {
    const parts = colorPath.split('.');
    let current: unknown = designTokens.colors;
    
    for (const part of parts) {
      if (typeof current === 'object' && current !== null && part in current) {
        current = (current as Record<string, unknown>)[part];
      } else {
        throw new Error(`Color not found: ${colorPath}`);
      }
    }
    
    return current as string;
  },
  
  // 获取间距
  getSpacing: (size: keyof SpacingTokens): string => {
    return designTokens.spacing[size];
  },
  
  // 获取圆角
  getBorderRadius: (size: keyof BorderRadiusTokens): string => {
    return designTokens.borderRadius[size];
  },
  
  // 获取阴影
  getShadow: (size: keyof ShadowTokens): string => {
    return designTokens.shadows[size];
  },
  
  // 检查颜色使用是否符合规范
  validateColorUsage: (scenario: string, colorPath: string): boolean => {
    // 检查是否为品牌蓝
    if (colorPath.startsWith('blue.')) {
      // 检查是否在允许的场景中
      if (!colorUsageRules.blueUsageScenarios.includes(scenario)) {
        return false;
      }
    }
    
    return true;
  },
  
  // 生成透明度变体
  withOpacity: (color: string, opacity: number): string => {
    const alpha = Math.round(opacity * 255).toString(16).padStart(2, '0');
    return color + alpha;
  },
};

export default designTokens;