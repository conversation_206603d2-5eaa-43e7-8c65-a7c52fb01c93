/**
 * 硬编码颜色替换工具
 * 将项目中的硬编码颜色替换为设计令牌
 */

// 颜色映射表 - 硬编码颜色到设计令牌的映射
export const colorMappings = {
  // 基础色
  '#ffffff': 'designTokens.colors.white',
  '#000000': 'designTokens.colors.black',
  
  // 灰色系
  '#f9f9f9': 'designTokens.colors.gray.50',
  '#f5f5f5': 'designTokens.colors.gray.100',
  '#e5e5e5': 'designTokens.colors.gray.200',
  '#d4d4d4': 'designTokens.colors.gray.300',
  '#a3a3a3': 'designTokens.colors.gray.400',
  '#737373': 'designTokens.colors.gray.500',
  '#525252': 'designTokens.colors.gray.600',
  '#404040': 'designTokens.colors.gray.700',
  '#262626': 'designTokens.colors.gray.800',
  '#171717': 'designTokens.colors.gray.900',
  
  // 品牌蓝
  '#eff6ff': 'designTokens.colors.blue.50',
  '#dbeafe': 'designTokens.colors.blue.100',
  '#bfdbfe': 'designTokens.colors.blue.200',
  '#93c5fd': 'designTokens.colors.blue.300',
  '#60a5fa': 'designTokens.colors.blue.400',
  '#3b82f6': 'designTokens.colors.blue.500',
  '#2563eb': 'designTokens.colors.blue.600',
  '#1d4ed8': 'designTokens.colors.blue.700',
  '#1e40af': 'designTokens.colors.blue.800',
  '#1e3a8a': 'designTokens.colors.blue.900',
  
  // 功能色
  '#ef4444': 'designTokens.colors.red.500',
  '#dc2626': 'designTokens.colors.red.600',
  '#22c55e': 'designTokens.colors.green.500',
  '#16a34a': 'designTokens.colors.green.600',
  '#eab308': 'designTokens.colors.yellow.500',
  '#ca8a04': 'designTokens.colors.yellow.600',
  
  // 其他常见颜色
  '#4b5563': 'designTokens.colors.gray.600', // 次要文字
  '#111827': 'designTokens.colors.gray.900', // 主要文字
  '#374151': 'designTokens.colors.gray.700', // 深色文字
  '#e5e7eb': 'designTokens.colors.gray.200', // 边框
  '#10b981': 'designTokens.colors.green.500', // 成功绿
  '#f59e0b': 'designTokens.colors.yellow.500', // 警告黄
  '#8882': 'designTokens.colors.gray.300', // 网格线
  '#888888': 'designTokens.colors.gray.400', // 中灰
  
  // 特殊处理颜色
  '#e2e8f0': 'designTokens.colors.gray.200', // 滚动条
  '#cbd5e1': 'designTokens.colors.gray.300', // 滚动条悬停
  '#7c3aed': 'designTokens.colors.blue.600', // Logo紫色 - 替换为品牌蓝
  '#4f46e5': 'designTokens.colors.blue.700', // Logo深紫 - 替换为深蓝
  '#FFDD00': 'designTokens.colors.yellow.500', // 咖啡按钮
  '#e6f7f230': 'designTokens.colors.blue.50', // 渐变覆盖
  '#00000005': 'designTokens.colors.black', // 极低透明度黑
  '#ffffff20': 'designTokens.colors.white', // 低透明度白
} as const;

// Tailwind CSS 类映射
export const tailwindColorMappings = {
  // 背景色
  'bg-white': 'bg-white',
  'bg-black': 'bg-black',
  'bg-gray-50': 'bg-gray-50',
  'bg-gray-100': 'bg-gray-100',
  'bg-gray-200': 'bg-gray-200',
  'bg-gray-300': 'bg-gray-300',
  'bg-gray-400': 'bg-gray-400',
  'bg-gray-500': 'bg-gray-500',
  'bg-gray-600': 'bg-gray-600',
  'bg-gray-700': 'bg-gray-700',
  'bg-gray-800': 'bg-gray-800',
  'bg-gray-900': 'bg-gray-900',
  'bg-blue-50': 'bg-blue-50',
  'bg-blue-100': 'bg-blue-100',
  'bg-blue-200': 'bg-blue-200',
  'bg-blue-300': 'bg-blue-300',
  'bg-blue-400': 'bg-blue-400',
  'bg-blue-500': 'bg-blue-500',
  'bg-blue-600': 'bg-blue-600',
  'bg-blue-700': 'bg-blue-700',
  'bg-blue-800': 'bg-blue-800',
  'bg-blue-900': 'bg-blue-900',
  
  // 文字色
  'text-white': 'text-white',
  'text-black': 'text-black',
  'text-gray-50': 'text-gray-50',
  'text-gray-100': 'text-gray-100',
  'text-gray-200': 'text-gray-200',
  'text-gray-300': 'text-gray-300',
  'text-gray-400': 'text-gray-400',
  'text-gray-500': 'text-gray-500',
  'text-gray-600': 'text-gray-600',
  'text-gray-700': 'text-gray-700',
  'text-gray-800': 'text-gray-800',
  'text-gray-900': 'text-gray-900',
  'text-blue-50': 'text-blue-50',
  'text-blue-100': 'text-blue-100',
  'text-blue-200': 'text-blue-200',
  'text-blue-300': 'text-blue-300',
  'text-blue-400': 'text-blue-400',
  'text-blue-500': 'text-blue-500',
  'text-blue-600': 'text-blue-600',
  'text-blue-700': 'text-blue-700',
  'text-blue-800': 'text-blue-800',
  'text-blue-900': 'text-blue-900',
  
  // 边框色
  'border-white': 'border-white',
  'border-black': 'border-black',
  'border-gray-50': 'border-gray-50',
  'border-gray-100': 'border-gray-100',
  'border-gray-200': 'border-gray-200',
  'border-gray-300': 'border-gray-300',
  'border-gray-400': 'border-gray-400',
  'border-gray-500': 'border-gray-500',
  'border-gray-600': 'border-gray-600',
  'border-gray-700': 'border-gray-700',
  'border-gray-800': 'border-gray-800',
  'border-gray-900': 'border-gray-900',
  'border-blue-50': 'border-blue-50',
  'border-blue-100': 'border-blue-100',
  'border-blue-200': 'border-blue-200',
  'border-blue-300': 'border-blue-300',
  'border-blue-400': 'border-blue-400',
  'border-blue-500': 'border-blue-500',
  'border-blue-600': 'border-blue-600',
  'border-blue-700': 'border-blue-700',
  'border-blue-800': 'border-blue-800',
  'border-blue-900': 'border-blue-900',
} as const;

// 颜色替换工具类
export class ColorReplacer {
  /**
   * 替换字符串中的硬编码颜色
   */
  static replaceHardcodedColors(content: string): string {
    let result = content;
    
    // 替换十六进制颜色
    for (const [hexColor, tokenPath] of Object.entries(colorMappings)) {
      const regex = new RegExp(hexColor, 'g');
      result = result.replace(regex, `/* ${hexColor} -> ${tokenPath} */ ${tokenPath}`);
    }
    
    return result;
  }
  
  /**
   * 替换Tailwind CSS类中的硬编码颜色
   */
  static replaceTailwindColors(content: string): string {
    let result = content;
    
    // 替换内联样式中的颜色
    result = result.replace(/style=\{[^}]*color:\s*["']?#[0-9a-fA-F]{3,6}["']?[^}]*\}/g, (match) => {
      return this.replaceHardcodedColors(match);
    });
    
    // 替换backgroundColor
    result = result.replace(/backgroundColor:\s*["']?#[0-9a-fA-F]{3,6}["']?/g, (match) => {
      return this.replaceHardcodedColors(match);
    });
    
    // 替换borderColor
    result = result.replace(/borderColor:\s*["']?#[0-9a-fA-F]{3,6}["']?/g, (match) => {
      return this.replaceHardcodedColors(match);
    });
    
    return result;
  }
  
  /**
   * 分析文件中的颜色使用情况
   */
  static analyzeColors(content: string): ColorAnalysis {
    const hexColors = content.match(/#[0-9a-fA-F]{3,6}/g) || [];
    const uniqueColors = [...new Set(hexColors)];
    
    const analysis: ColorAnalysis = {
      totalColors: hexColors.length,
      uniqueColors: uniqueColors.length,
      colors: {},
    };
    
    for (const color of uniqueColors) {
      const count = hexColors.filter(c => c === color).length;
      analysis.colors[color] = {
        count,
        replacement: colorMappings[color as keyof typeof colorMappings] || 'UNKNOWN',
        priority: this.getColorPriority(color),
      };
    }
    
    return analysis;
  }
  
  /**
   * 获取颜色替换优先级
   */
  static getColorPriority(color: string): 'high' | 'medium' | 'low' {
    const highPriorityColors = ['#3b82f6', '#2563eb', '#000000', '#ffffff'];
    const mediumPriorityColors = ['#8882', '#4b5563', '#111827', '#374151'];
    
    if (highPriorityColors.includes(color)) return 'high';
    if (mediumPriorityColors.includes(color)) return 'medium';
    return 'low';
  }
  
  /**
   * 生成颜色替换报告
   */
  static generateReport(analysis: ColorAnalysis): string {
    let report = '颜色使用分析报告\n';
    report += '================\n\n';
    report += `总颜色数量: ${analysis.totalColors}\n`;
    report += `唯一颜色数量: ${analysis.uniqueColors}\n\n`;
    
    const colorsByPriority = {
      high: Object.entries(analysis.colors).filter(([color, data]) => {
        console.log(`Processing high priority color: ${color}`);
        return data.priority === 'high';
      }),
      medium: Object.entries(analysis.colors).filter(([color, data]) => {
        console.log(`Processing medium priority color: ${color}`);
        return data.priority === 'medium';
      }),
      low: Object.entries(analysis.colors).filter(([color, data]) => {
        console.log(`Processing low priority color: ${color}`);
        return data.priority === 'low';
      }),
    };
    
    // 输出优先级统计用于调试
    console.log('Colors by priority:', {
      high: colorsByPriority.high.length,
      medium: colorsByPriority.medium.length,
      low: colorsByPriority.low.length
    });
    
    report += '高优先级颜色:\n';
    for (const [color, data] of colorsByPriority.high) {
      report += `  ${color}: ${data.count}次 -> ${data.replacement}\n`;
    }
    
    report += '\n中等优先级颜色:\n';
    for (const [color, data] of colorsByPriority.medium) {
      report += `  ${color}: ${data.count}次 -> ${data.replacement}\n`;
    }
    
    report += '\n低优先级颜色:\n';
    for (const [color, data] of colorsByPriority.low) {
      report += `  ${color}: ${data.count}次 -> ${data.replacement}\n`;
    }
    
    return report;
  }
}

// 类型定义
export interface ColorAnalysis {
  totalColors: number;
  uniqueColors: number;
  colors: Record<string, {
    count: number;
    replacement: string;
    priority: 'high' | 'medium' | 'low';
  }>;
}

// 工具函数
export const colorUtils = {
  /**
   * 检查颜色是否在设计令牌中
   */
  isColorInTokens(color: string): boolean {
    return color in colorMappings;
  },
  
  /**
   * 获取颜色的设计令牌路径
   */
  getColorTokenPath(color: string): string | undefined {
    return colorMappings[color as keyof typeof colorMappings];
  },
  
  /**
   * 验证颜色替换
   */
  validateColorReplacement(originalColor: string, replacement: string): boolean {
    const tokenPath = colorMappings[originalColor as keyof typeof colorMappings];
    return tokenPath === replacement;
  },
  
  /**
   * 获取替换建议
   */
  getReplacementSuggestion(color: string): string {
    const tokenPath = colorMappings[color as keyof typeof colorMappings];
    if (tokenPath) {
      return `使用设计令牌: ${tokenPath}`;
    }
    return '未找到匹配的设计令牌，需要手动处理';
  },
};

export default ColorReplacer;